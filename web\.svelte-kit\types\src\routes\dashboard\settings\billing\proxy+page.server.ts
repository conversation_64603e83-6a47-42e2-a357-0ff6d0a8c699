// @ts-nocheck
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types.js';
import { getPlanById, initializePlansInDatabase } from '$lib/server/plan-sync';

export const load = async ({ locals }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user || !user.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      subscriptions: {
        orderBy: { createdAt: 'desc' },
        take: 1,
      },
    },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  // Initialize plans in database if they don't exist
  await initializePlansInDatabase();

  // Get the plan from the database based on user role
  let currentPlan = null;

  try {
    // Always try to get the plan from the database
    currentPlan = await getPlanById(userData.role || 'free');

    if (!currentPlan) {
      console.error(`Plan not found for role: ${userData.role} after initialization.`);
      throw new Error(`Plan not found for role: ${userData.role}`);
    }
  } catch (error) {
    console.error('Error getting plan from database:', error);
    throw error;
  }

  // Get feature usage data
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  // Get resume scans usage
  const resumeScansUsed = await prisma.documentSubmission.count({
    where: {
      userId: userData.id,
      createdAt: { gte: startOfMonth },
    },
  });

  // Get profiles usage
  const profilesUsed = await prisma.profile.count({
    where: {
      userId: userData.id,
    },
  });

  // Get feature usage from the database - commented out until the schema is updated
  // const featureUsage = await prisma.featureUsage.findMany({
  //   where: {
  //     userId: userData.id,
  //   },
  //   include: {
  //     feature: true,
  //     limit: true,
  //   },
  // });

  // Get feature usage data from the database
  // Note: Using any type until we have proper types for the feature usage
  let formattedUsage: any[] = [];

  try {
    // First check if the tables exist
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'web' AND table_name = 'FeatureUsage'
      );
    `;

    const tablesExist =
      Array.isArray(tableExists) && tableExists.length > 0 && tableExists[0].exists;

    if (tablesExist) {
      // Check if the featureUsage table exists
      // Use a simpler approach to avoid SQL errors
      // First get the feature usage records
      const usageRecords = await prisma.featureUsage.findMany({
        where: {
          userId: userData.id,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      // Then get the related features and limits
      const featureIds = [...new Set(usageRecords.map((record) => record.featureId))];
      const limitIds = [...new Set(usageRecords.map((record) => record.limitId))];

      const features = await prisma.feature.findMany({
        where: {
          id: {
            in: featureIds,
          },
        },
      });

      const limits = await prisma.featureLimit.findMany({
        where: {
          id: {
            in: limitIds,
          },
        },
      });

      // Create a map for quick lookups
      const featureMap = features.reduce((acc, feature) => {
        acc[feature.id] = feature;
        return acc;
      }, {});

      const limitMap = limits.reduce((acc, limit) => {
        acc[limit.id] = limit;
        return acc;
      }, {});

      // Combine the data
      const featureUsage = usageRecords.map((usage) => ({
        id: usage.id,
        userId: usage.userId,
        featureId: usage.featureId,
        limitId: usage.limitId,
        used: usage.used,
        period: usage.period,
        updatedAt: usage.updatedAt,
        featureName: featureMap[usage.featureId]?.name || 'Unknown Feature',
        featureDescription: featureMap[usage.featureId]?.description || '',
        limitName: limitMap[usage.limitId]?.name || 'Unknown Limit',
        limitValue: limitMap[usage.limitId]?.defaultValue || '0',
        limitType: limitMap[usage.limitId]?.type || 'monthly',
      }));

      // Format the feature usage data
      formattedUsage = Array.isArray(featureUsage)
        ? featureUsage.map((usage: any) => {
            // Calculate percentage used if there's a limit
            let percentUsed = null;
            if (usage.limitType === 'monthly') {
              const limitValue = parseInt(usage.limitValue, 10);
              percentUsed = limitValue > 0 ? Math.min(100, (usage.used / limitValue) * 100) : 0;
            }

            return {
              id: usage.id,
              featureId: usage.featureId,
              featureName: usage.featureName,
              limitId: usage.limitId,
              limitName: usage.limitName,
              used: usage.used,
              limit: usage.limitValue,
              percentUsed,
              period: usage.period,
              updatedAt: usage.updatedAt,
            };
          })
        : [];
    } else {
      formattedUsage = [];
    }
  } catch (error) {
    console.error('Error fetching feature usage:', error);
    // If there's an error, return an empty array
    formattedUsage = [];
  }

  // Get the resumeScans limit as a number
  let resumeScansLimit = 10;
  if (currentPlan?.limits?.resumesPerMonth) {
    const limitValue = String(currentPlan.limits.resumesPerMonth);
    // Handle string format like "15/mo"
    const parts = limitValue.split('/');
    resumeScansLimit = parseInt(parts[0], 10) || 10;
  }

  // Get the profiles limit as a number
  let profilesLimit = 1;
  if (currentPlan?.limits?.profiles) {
    const limitValue = String(currentPlan.limits.profiles);
    profilesLimit = parseInt(limitValue, 10) || 1;
  }

  // Calculate remaining values
  const resumeScansRemaining = Math.max(0, resumeScansLimit - resumeScansUsed);

  // Initialize Stripe if the user has a customer ID
  let paymentMethods = [];
  let invoices = [];
  let upcomingInvoice = null;
  let subscriptionData = null;
  let defaultPaymentMethod = null;

  if (userData.stripeCustomerId) {
    const isProd = process.env.NODE_ENV === 'production';
    const stripeSecret = isProd
      ? process.env.STRIPE_SECRET_KEY_LIVE || 'sk_live_placeholder'
      : process.env.STRIPE_SECRET_KEY_TEST || 'sk_test_placeholder';

    // Import Stripe dynamically
    const Stripe = (await import('stripe')).default;
    const stripe = new Stripe(stripeSecret, {
      apiVersion: '2025-04-30.basil',
    });

    try {
      // Get payment methods
      const paymentMethodsResponse = await stripe.paymentMethods.list({
        customer: userData.stripeCustomerId,
        type: 'card',
      });
      paymentMethods = paymentMethodsResponse.data;

      // Get customer to find default payment method
      const customer = await stripe.customers.retrieve(userData.stripeCustomerId);
      if (
        customer &&
        !('deleted' in customer) &&
        customer.invoice_settings?.default_payment_method
      ) {
        defaultPaymentMethod = customer.invoice_settings.default_payment_method;
      }

      // Mark default payment method
      paymentMethods = paymentMethods.map((method) => ({
        ...method,
        isDefault: method.id === defaultPaymentMethod,
      }));

      // Get invoices without expanded data
      // Note: In newer Stripe API versions, neither payment_intent nor charge expansion is available
      const invoicesResponse = await stripe.invoices.list({
        customer: userData.stripeCustomerId,
        limit: 10,
      });
      invoices = invoicesResponse.data;

      // Get upcoming invoice if there's an active subscription
      try {
        upcomingInvoice = null;
      } catch (err) {
        console.log('No upcoming invoice found:', err);
      }

      // Get all subscriptions with expanded payment method and plan details
      // Note: Stripe limits expansion to 4 levels deep
      const subscriptionsResponse = await stripe.subscriptions.list({
        customer: userData.stripeCustomerId,
        limit: 5, // Get more than just active subscriptions
        expand: ['data.default_payment_method', 'data.items.data.price'],
      });

      if (subscriptionsResponse.data.length > 0) {
        // Find the most relevant subscription - prioritize active, then trialing, then others
        let subscription = subscriptionsResponse.data.find((sub) => sub.status === 'active');

        if (!subscription) {
          subscription = subscriptionsResponse.data.find((sub) => sub.status === 'trialing');
        }

        if (!subscription) {
          // Fall back to the first subscription if no active or trialing ones
          subscription = subscriptionsResponse.data[0];
        }

        // Get the payment method details if available
        let paymentMethodDetails = null;
        if (subscription.default_payment_method) {
          // Type assertion to handle Stripe types
          const paymentMethod =
            typeof subscription.default_payment_method === 'string'
              ? null // Skip if it's just a string ID
              : subscription.default_payment_method;

          if (paymentMethod && paymentMethod.card) {
            paymentMethodDetails = {
              id: paymentMethod.id,
              brand: paymentMethod.card.brand,
              last4: paymentMethod.card.last4,
              expMonth: paymentMethod.card.exp_month,
              expYear: paymentMethod.card.exp_year,
            };
          }
        }

        // Get the price details
        const subscriptionItem = subscription.items.data[0];
        const price = subscriptionItem?.price;
        const productId = price?.product as string;

        // Get the billing cycle (monthly or annual)
        const interval = price?.recurring?.interval || 'month';
        const intervalCount = price?.recurring?.interval_count || 1;
        const billingCycle = interval === 'year' || intervalCount > 1 ? 'annual' : 'monthly';

        // Fetch the product details separately
        let productName = null;
        let productDescription = null;

        if (productId) {
          try {
            const product = await stripe.products.retrieve(productId);
            productName = product.name;
            productDescription = product.description;
          } catch (error) {
            console.error('Error fetching product details:', error);
          }
        }

        // Type assertion for Stripe subscription properties
        const stripeSubscription = subscription as any;

        // Check for pause_collection and metadata
        const pauseCollection = stripeSubscription.pause_collection || null;
        const metadata = stripeSubscription.metadata || {};

        // Check if subscription is paused at Stripe level
        const isPaused =
          pauseCollection !== null ||
          metadata.pause_at_period_end === 'true' ||
          stripeSubscription.status === 'paused' ||
          stripeSubscription.status === 'pausing_at_period_end' ||
          (stripeSubscription.cancel_at_period_end && metadata.action_at_period_end === 'pause');

        // Get the date when the plan will change back to free
        // Always use the current period end date for when the plan changes back to free
        // This ensures we always have a valid date
        const planChangesOnDate = new Date(stripeSubscription.current_period_end * 1000);

        // Get period dates from subscription items if they're not available at the top level
        // In some Stripe API versions, these dates are in the subscription items
        let startTimestamp = stripeSubscription.current_period_start;
        let endTimestamp = stripeSubscription.current_period_end;

        // Check if we need to get dates from subscription items
        if (!startTimestamp && subscription.items?.data?.length > 0) {
          const firstItem = subscription.items.data[0];

          if (firstItem.current_period_start) {
            startTimestamp = firstItem.current_period_start;
          }

          if (firstItem.current_period_end) {
            endTimestamp = firstItem.current_period_end;
          }
        }

        // Convert Unix timestamps (seconds) to JavaScript Date objects
        const startDate = startTimestamp ? new Date(startTimestamp * 1000) : null;
        const endDate = endTimestamp ? new Date(endTimestamp * 1000) : null;

        // Check if subscription is cancelled and get cancelled date
        const isCancelled =
          stripeSubscription.status === 'canceled' || stripeSubscription.status === 'cancelled';
        let cancelledDate = null;

        if (isCancelled && stripeSubscription.canceled_at) {
          cancelledDate = new Date(stripeSubscription.canceled_at * 1000);
        }

        subscriptionData = {
          id: subscription.id,
          status: subscription.status,
          // Include both camelCase and snake_case versions for compatibility
          currentPeriodStart: startDate,
          currentPeriodEnd: endDate,
          current_period_start: startDate,
          current_period_end: endDate,
          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
          // Include cancelled date if available
          canceled_at: cancelledDate,
          canceledAt: cancelledDate,
          paymentMethod: paymentMethodDetails,
          items: subscription.items.data,
          // Include pause collection data
          pause_collection: pauseCollection,
          // Include metadata
          metadata: metadata,
          // Add a simple flag for paused status
          isPaused: isPaused,
          // Add the date when the plan changes back to free
          planChangesOnDate: planChangesOnDate,
          price: {
            id: price?.id,
            unitAmount: price?.unit_amount,
            currency: price?.currency,
            interval,
            intervalCount,
            billingCycle,
          },
          product: {
            id: productId,
            name: productName,
            description: productDescription,
          },
        };

        // Update the current plan based on subscription data if needed
        if (subscriptionData.product.name && (!currentPlan || currentPlan.id === 'free')) {
          try {
            // Try to find a matching plan by ID directly
            const planId = subscriptionData.product.name.toLowerCase().replace(/\s+/g, '_');
            const matchingPlan = await getPlanById(planId);

            if (matchingPlan) {
              currentPlan = matchingPlan;
            }
          } catch (error) {
            console.error('Error finding matching plan:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching Stripe data:', error);
    }
  }

  // Make sure we explicitly include the stripeCustomerId in the user object
  const userWithUsage = {
    id: userData.id,
    email: userData.email,
    name: userData.name,
    role: userData.role,
    stripeCustomerId: userData.stripeCustomerId,
    usage:
      formattedUsage.length > 0
        ? formattedUsage
        : [
            {
              featureId: 'resume_scanner',
              featureName: 'Resume Scanner',
              limitId: 'resume_scans_per_month',
              limitName: 'Resume Scans per Month',
              used: resumeScansUsed,
              limit: resumeScansLimit,
              remaining: resumeScansRemaining,
              percentUsed: (resumeScansUsed / resumeScansLimit) * 100,
            },
            {
              featureId: 'job_search_profiles',
              featureName: 'Job Search Profiles',
              limitId: 'job_search_profiles',
              limitName: 'Job Search Profiles',
              used: profilesUsed,
              limit: profilesLimit,
              remaining: Math.max(0, profilesLimit - profilesUsed),
              percentUsed: (profilesUsed / profilesLimit) * 100,
            },
          ],
  };

  return {
    user: userWithUsage,
    billing: {
      currentPlan,
      paymentMethods,
      invoices,
      upcomingInvoice,
      subscription: subscriptionData,
      defaultPaymentMethod,
    },
  };
};

// @ts-nocheck
import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const profileSchema = z.object({
  name: z.string().min(1, 'Profile name is required'),
  jobType: z.string().min(1, 'Job type is required'),
  industry: z.string().optional(),
  resumeId: z.string().optional(),
});

export const load = async ({ locals, url }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user || !user.email) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data with team memberships
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      TeamMember: {
        include: {
          team: true,
        },
      },
    },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = userData;

  // Get query parameters for filtering and pagination
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '12');
  const search = url.searchParams.get('search') || '';
  const jobType = url.searchParams.get('jobType') || '';
  const industry = url.searchParams.get('industry') || '';
  const owner = url.searchParams.get('owner') || 'all'; // 'all', 'user', 'team'

  const offset = (page - 1) * limit;

  // Check if user has team access
  const userTeams = userData.TeamMember.map((tm) => tm.teamId);
  const hasTeamAccess = userTeams.length > 0;

  // Build where clause for profiles
  const whereClause: any = {
    OR: [
      { userId: userData.id },
      ...(hasTeamAccess
        ? [
            {
              teamId: {
                in: userTeams,
              },
            },
          ]
        : []),
    ],
  };

  // Add search filter
  if (search) {
    whereClause.name = {
      contains: search,
      mode: 'insensitive',
    };
  }

  // Add owner filter
  if (owner === 'user') {
    whereClause.OR = [{ userId: userData.id }];
  } else if (owner === 'team' && hasTeamAccess) {
    whereClause.OR = [
      {
        teamId: {
          in: userTeams,
        },
      },
    ];
  }

  // Get user's profiles with pagination
  const profiles = await prisma.profile.findMany({
    where: whereClause,
    orderBy: { updatedAt: 'desc' },
    take: limit,
    skip: offset,
    include: {
      defaultDocument: true,
      data: true,
      team: {
        select: {
          id: true,
          name: true,
        },
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  // Get total count for pagination
  const totalProfiles = await prisma.profile.count({
    where: whereClause,
  });

  // Get user's documents with resumes for the form
  const documents = await prisma.document.findMany({
    where: {
      userId: userData.id,
      resume: { isNot: null },
    },
    include: {
      resume: true,
    },
    orderBy: { updatedAt: 'desc' },
  });

  // Format documents for the form
  const formattedDocuments = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName,
    resume: doc.resume,
  }));

  // Get unique job types and industries for filtering
  const allProfiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: userData.id },
        ...(hasTeamAccess
          ? [
              {
                teamId: {
                  in: userTeams,
                },
              },
            ]
          : []),
      ],
    },
    include: {
      data: true,
    },
  });

  const jobTypes = new Set<string>();
  const industries = new Set<string>();

  allProfiles.forEach((profile) => {
    if (profile.data?.data) {
      try {
        // Handle both string and object data
        const dataStr =
          typeof profile.data.data === 'string'
            ? profile.data.data
            : JSON.stringify(profile.data.data);
        const data = JSON.parse(dataStr);
        if (data.jobType && typeof data.jobType === 'string') jobTypes.add(data.jobType);
        if (data.industry && typeof data.industry === 'string') industries.add(data.industry);
      } catch (e) {
        console.warn('Failed to parse profile data:', e);
      }
    }
  });

  // Create an empty form for new profile creation
  const form = await superValidate(
    {
      name: '',
      jobType: '',
      industry: '',
      resumeId: '',
    },
    zod(profileSchema)
  );

  // Calculate pagination info
  const totalPages = Math.ceil(totalProfiles / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    user: userData,
    profiles,
    documents: formattedDocuments,
    form,
    pagination: {
      page,
      limit,
      totalProfiles,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
    filters: {
      search,
      jobType,
      industry,
      owner,
      jobTypes: Array.from(jobTypes).sort((a, b) => a.localeCompare(b)),
      industries: Array.from(industries).sort((a, b) => a.localeCompare(b)),
    },
    hasTeamAccess,
  };
};

export const actions = {
  default: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const tokenData = await getUserFromToken(cookies);

    if (!tokenData?.email) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(profileSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Create a new profile
      const newProfile = await prisma.profile.create({
        data: {
          name: form.data.name,
          userId: userData.id,
          ...(form.data.resumeId &&
            form.data.resumeId !== '' && { defaultDocumentId: form.data.resumeId }),
          data: {
            create: {
              data: JSON.stringify({
                jobType: form.data.jobType,
                industry: form.data.industry || null,
              }),
            },
          },
        },
      });

      // Return with success message and the new profile
      return { form, success: true, profile: newProfile };
    } catch (error) {
      console.error('Error updating profile:', error);
      return fail(500, { form, error: 'Failed to update profile' });
    }
  },
};
;null as any as Actions;
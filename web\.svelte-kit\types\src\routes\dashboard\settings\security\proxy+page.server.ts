// @ts-nocheck
import { z } from 'zod';
import { fail, redirect, json } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import bcrypt from 'bcryptjs';
import { prisma } from '$lib/server/prisma';
import {
  getUserFromToken,
  verifySessionToken,
  getAuthLocation,
  cleanupUserSessions,
} from '$lib/server/auth';
import {
  generateSecret,
  generateQRCode,
  verifyToken,
  authenticator,
  generateBackupCodes,
  formatBackupCode,
} from '$lib/server/twoFactor';
import {
  generatePasskeyRegistrationOptions,
  verifyPasskeyRegistration,
} from '$lib/server/webauthn';

// Define the RegistrationResponseJSON type if it's not available
type RegistrationResponseJSON = any;
import type { Actions, PageServerLoad } from './$types';
import * as UAParser from 'ua-parser-js';

// Define schemas for validation
const passwordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

const twoFactorSchema = z.object({
  enabled: z.boolean().default(false),
  method: z.enum(['app', 'sms']).default('app'),
  verificationCode: z.string().min(6, 'Verification code is required').max(6).optional(),
});

const passkeysSchema = z.object({
  name: z.string().min(1, 'Passkey name is required'),
});

const phoneSchema = z.object({
  phoneNumber: z.string().min(1, 'Phone number is required'),
  verificationCode: z.string().min(6, 'Verification code is required').max(6).optional(),
});

export const load = async ({ locals, cookies }: Parameters<PageServerLoad>[0]) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Fetch the complete user data from the database to get preferences
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  // Create the password form
  const passwordForm = await superValidate(zod(passwordSchema));

  // Extract two-factor preferences
  const preferences = (userData.preferences as any) || {};
  const securityPrefs = preferences.security || {};

  // Create the passkeys form
  const passkeysForm = await superValidate(zod(passkeysSchema));

  // Get existing passkeys
  let passkeys = securityPrefs.passkeys || [];

  // Ensure passkeys is an array
  if (!Array.isArray(passkeys)) {
    console.error('Passkeys is not an array in user preferences:', passkeys);
    passkeys = [];
  }

  // Normalize passkey format to ensure consistent display
  passkeys = passkeys.map((passkey: any) => {
    // Ensure all required fields exist
    return {
      id: passkey.id || passkey.credentialID || 'unknown-id',
      name: passkey.name || 'Unnamed Passkey',
      credentialID: passkey.credentialID || passkey.id || 'unknown-id',
      credentialPublicKey: passkey.credentialPublicKey || passkey.publicKey || '',
      counter: passkey.counter || 0,
      transports: passkey.transports || [],
      createdAt: passkey.createdAt || new Date().toISOString(),
      lastUsed: passkey.lastUsed || passkey.createdAt || new Date().toISOString(),
    };
  });

  // Get active sessions for the user
  const sessions = await prisma.session.findMany({
    where: {
      userId: user.id,
      isRevoked: false,
      expires: { gt: new Date() },
    },
    orderBy: { lastActive: 'desc' },
  });

  // Get the current token
  const token = cookies.get('auth_token');

  // Find the current session
  const currentSession = await prisma.session.findFirst({
    where: {
      sessionToken: token,
      isRevoked: false,
      expires: { gt: new Date() },
    },
  });

  if (!currentSession) {
    console.log('Warning: Current session not found for token:', token?.substring(0, 10) + '...');
  } else {
    console.log('Current session found:', currentSession.id);
  }

  // Group sessions by device+browser+os to identify duplicates
  const sessionsByDevice = new Map();

  sessions.forEach((session) => {
    const deviceKey = `${session.device || ''}|${session.browser || ''}|${session.os || ''}`;

    // If this is the current session, always keep it
    const isCurrent = currentSession ? session.id === currentSession.id : session.token === token;

    if (isCurrent) {
      // Always keep the current session and mark it as current
      sessionsByDevice.set(deviceKey, {
        session,
        isCurrent: true,
      });
    } else if (!sessionsByDevice.has(deviceKey)) {
      // If we don't have a session for this device yet, add it
      sessionsByDevice.set(deviceKey, {
        session,
        isCurrent: false,
      });
    } else {
      // If we already have a session for this device and it's not marked as current,
      // keep the most recently active one
      const existing = sessionsByDevice.get(deviceKey);
      if (!existing.isCurrent && session.lastActive > existing.session.lastActive) {
        sessionsByDevice.set(deviceKey, {
          session,
          isCurrent: false,
        });
      }
    }
  });

  // Convert the map back to an array and format the sessions
  const formattedSessions = Array.from(sessionsByDevice.values()).map(({ session, isCurrent }) => ({
    id: session.id,
    device: session.device || 'Unknown device',
    browser: session.browser || 'Unknown browser',
    os: session.os || 'Unknown OS',
    ip: session.ip || 'Unknown IP',
    location: session.location || 'Unknown location',
    lastActive: session.lastActive,
    createdAt: session.expires,
    isCurrent,
  }));

  return {
    passwordForm,
    passkeysForm,
    passkeys,
    sessions: formattedSessions,
    userData: {
      id: userData.id,
      email: userData.email,
      hasPreferences: !!userData.preferences,
    },
  };
};

export const actions = {
  // Password change action
  changePassword: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const tokenData = await getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(passwordSchema));

    if (!form.valid) {
      return fail(400, { passwordForm: form });
    }

    try {
      const userWithPassword = await prisma.user.findUnique({
        where: { id: userData.id },
        select: { passwordHash: true },
      });

      if (!userWithPassword?.passwordHash) {
        return fail(400, {
          passwordForm: form,
          error: 'Cannot update password for this account type',
        });
      }

      const isCurrentPasswordValid = await bcrypt.compare(
        form.data.currentPassword,
        userWithPassword.passwordHash
      );

      if (!isCurrentPasswordValid) {
        form.errors.currentPassword = ['Current password is incorrect'];
        return fail(400, { passwordForm: form });
      }

      const newPasswordHash = await bcrypt.hash(form.data.newPassword, 10);
      await prisma.user.update({
        where: { id: userData.id },
        data: { passwordHash: newPasswordHash },
      });

      return { passwordForm: form, success: true };
    } catch (error) {
      console.error('Error updating password:', error);
      return fail(500, { passwordForm: form, error: 'Failed to update password' });
    }
  },

  // Get registration options for a new passkey
  getRegistrationOptions: async ({ request, cookies }: import('./$types').RequestEvent) => {
    console.log('getRegistrationOptions called');

    try {
      const tokenData = await getUserFromToken(cookies);
      console.log('Token data:', tokenData);

      if (!tokenData || !tokenData.email) {
        console.log('Unauthorized: No token data or email');
        return json({ error: 'Unauthorized' }, { status: 401 });
      }

      // Get user data
      console.log('Getting user data for email:', tokenData.email);
      const userData = await prisma.user.findUnique({
        where: { email: tokenData.email },
      });
      console.log('User data found:', !!userData);

      if (!userData) {
        console.log('Unauthorized: No user data found');
        return json({ error: 'Unauthorized' }, { status: 401 });
      }

      try {
        // Get the name from either FormData or JSON
        console.log('Parsing request body');
        let name = '';

        const contentType = request.headers.get('content-type') || '';
        console.log('Content-Type:', contentType);

        if (contentType.includes('application/json')) {
          // Parse JSON
          console.log('Parsing JSON');
          try {
            const body = await request.json();
            name = body.name;
            console.log('Name from JSON:', name);
          } catch (jsonError) {
            console.error('Error parsing JSON:', jsonError);
          }
        } else {
          // Parse FormData
          console.log('Parsing FormData');
          try {
            const formData = await request.formData();
            name = formData.get('name') as string;
            console.log('Name from FormData:', name);
          } catch (formDataError) {
            console.error('Error parsing FormData:', formDataError);
          }
        }

        console.log('Final passkey name:', name);

        if (!name) {
          console.log('Passkey name is required');
          return json({ error: 'Passkey name is required' }, { status: 400 });
        }

        // Get existing passkeys
        console.log('Getting existing passkeys');
        const preferences = (userData.preferences as any) || {};
        const securityPrefs = preferences.security || {};
        const existingPasskeys = securityPrefs.passkeys || [];
        console.log('Existing passkeys count:', existingPasskeys.length);

        // Generate registration options
        console.log('Generating registration options');
        const options = await generatePasskeyRegistrationOptions(
          userData.id,
          userData.email,
          existingPasskeys.map((p: any) => ({
            id: p.credentialID,
            publicKey: p.credentialPublicKey,
            transports: p.transports || [],
          }))
        );
        console.log('Registration options generated');

        // Store the challenge in the database for verification later
        console.log('Storing challenge in database');
        const updatedPreferences = {
          ...preferences,
          security: {
            ...securityPrefs,
            currentChallenge: options.challenge,
          },
        };

        await prisma.user.update({
          where: { id: userData.id },
          data: {
            preferences: updatedPreferences,
          },
        });
        console.log('Challenge stored in database');

        console.log('Returning options');
        return json(options);
      } catch (innerError) {
        console.error('Inner error in getRegistrationOptions:', innerError);
        console.error('Inner error stack:', innerError.stack);
        return json({ error: 'Failed to process registration options' }, { status: 500 });
      }
    } catch (error) {
      console.error('Error generating passkey registration options:', error);
      console.error('Error stack:', error.stack);
      return json({ error: 'Failed to generate passkey registration options' }, { status: 500 });
    }
  },

  // Verify passkey registration
  verifyRegistration: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const tokenData = await getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
      console.log('verifyRegistration called');

      // Get the data from either FormData or JSON
      let name = '';
      let registrationResponse: any = null;

      const contentType = request.headers.get('content-type') || '';
      console.log('Content-Type:', contentType);

      if (contentType.includes('application/json')) {
        // Parse JSON
        console.log('Parsing JSON');
        try {
          const body = await request.json();
          name = body.name;
          registrationResponse = body.registrationResponse;
          console.log('Data from JSON:', { name, hasResponse: !!registrationResponse });
        } catch (jsonError) {
          console.error('Error parsing JSON:', jsonError);
        }
      } else {
        // Parse FormData
        console.log('Parsing FormData');
        try {
          const formData = await request.formData();
          name = formData.get('name') as string;
          const registrationResponseStr = formData.get('registrationResponse') as string;
          if (registrationResponseStr) {
            registrationResponse = JSON.parse(registrationResponseStr);
          }
          console.log('Data from FormData:', { name, hasResponse: !!registrationResponse });
        } catch (formDataError) {
          console.error('Error parsing FormData:', formDataError);
        }
      }

      if (!name || !registrationResponse) {
        console.log('Invalid request data');
        return json({ error: 'Invalid request data' }, { status: 400 });
      }

      // Get the stored challenge
      const preferences = (userData.preferences as any) || {};
      const securityPrefs = preferences.security || {};
      const challenge = securityPrefs.currentChallenge;

      if (!challenge) {
        return json({ error: 'No challenge found' }, { status: 400 });
      }

      // Verify the registration response
      const verification = await verifyPasskeyRegistration(
        registrationResponse as RegistrationResponseJSON,
        challenge
      );

      if (!verification.verified || !verification.registrationInfo) {
        return json({ error: 'Passkey verification failed' }, { status: 400 });
      }

      // Get existing passkeys
      const existingPasskeys = securityPrefs.passkeys || [];

      // Create a new passkey entry
      const newPasskey = {
        id: verification.registrationInfo.credentialID,
        name,
        credentialID: Buffer.from(verification.registrationInfo.credentialID).toString('base64url'),
        credentialPublicKey: Buffer.from(
          verification.registrationInfo.credentialPublicKey
        ).toString('base64url'),
        counter: verification.registrationInfo.counter,
        transports: registrationResponse.response.transports || [],
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
      };

      // Update the user's preferences with the new passkey
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          passkeys: [...existingPasskeys, newPasskey],
          currentChallenge: null, // Clear the challenge
        },
      };

      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: updatedPreferences,
        },
      });

      return json({
        success: true,
        passkeys: [...existingPasskeys, newPasskey],
      });
    } catch (error) {
      console.error('Error verifying passkey registration:', error);
      return json({ error: 'Failed to verify passkey registration' }, { status: 500 });
    }
  },

  // Remove a passkey
  removePasskey: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const tokenData = await getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      throw redirect(302, '/auth/sign-in');
    }

    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const formData = await request.formData();
    const passkeyId = formData.get('passkeyId')?.toString() || '';

    if (!passkeyId) {
      return fail(400, { error: 'Passkey ID is required' });
    }

    try {
      const preferences = (userData.preferences as any) || {};
      const securityPrefs = preferences.security || {};
      const existingPasskeys = securityPrefs.passkeys || [];

      // Filter out the passkey to remove
      const updatedPasskeys = existingPasskeys.filter((passkey: any) => passkey.id !== passkeyId);

      // Update the user's preferences
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          passkeys: updatedPasskeys,
        },
      };

      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: updatedPreferences,
        },
      });

      return json({
        type: 'success',
        data: { passkeys: updatedPasskeys },
      });
    } catch (error) {
      console.error('Error removing passkey:', error);
      return fail(500, { error: 'Failed to remove passkey' });
    }
  },

  // Send verification code
  sendVerificationCode: async ({ request, cookies }: import('./$types').RequestEvent) => {
    // Feature disabled
    return json(
      { success: false, error: 'Phone verification is currently disabled' },
      { status: 403 }
    );

    /* SMS feature is disabled - commenting out the implementation
    try {
      const data = await request.json();
      const { phoneNumber } = data;

      if (!phoneNumber) {
        return json({ success: false, error: 'Phone number is required' }, { status: 400 });
      }

      // Import the SMS service
      // const { sendVerificationCode } = await import('$lib/server/sms');

      // Send the verification code
      // const result = await sendVerificationCode(phoneNumber);

      // if (!result.success) {
      //   return json(
      //     { success: false, error: result.error || 'Failed to send verification code' },
      //     { status: 500 }
      //   );
      // }

      // Store the verification code in the user's preferences
      // const preferences = (userData.preferences as any) || {};
      // const securityPrefs = preferences.security || {};

      // await prisma.user.update({
      //   where: { id: userData.id },
      //   data: {
      //     preferences: {
      //       ...preferences,
      //       security: {
      //         ...securityPrefs,
      //         phone: {
      //           number: phoneNumber,
      //           verificationCode: result.code,
      //           codeExpires: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      //           verified: false,
      //         },
      //       },
      //     },
      //   },
      // });

      // return json({ success: true, codeSent: true });
    // } catch (error) {
    //   console.error('Error sending verification code:', error);
    //   return json({ success: false, error: 'Failed to send verification code' }, { status: 500 });
    // }
    */
  },

  // Update phone number
  updatePhone: async ({ request, cookies }: import('./$types').RequestEvent) => {
    // Feature disabled
    return { success: false, error: 'Phone verification is currently disabled' };

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const form = await superValidate(request, zod(phoneSchema));

    if (!form.valid) {
      return fail(400, { phoneForm: form });
    }

    try {
      // Get existing preferences or create an empty object
      const preferences = (userData.preferences as any) || {};
      const securityPrefs = preferences.security || {};

      // In a real implementation, you would send a verification code to the phone number
      // For now, we'll just update the phone number in the preferences
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          phone: {
            number: form.data.phoneNumber,
            verified: false, // Would be set to true after verification
            updatedAt: new Date().toISOString(),
          },
        },
      };

      // Update the user's preferences in the database
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: updatedPreferences,
        },
      });

      return { phoneForm: form, success: true, verificationSent: true };
    } catch (error) {
      console.error('Error updating phone number:', error);
      return fail(500, { phoneForm: form, error: 'Failed to update phone number' });
    }
  },

  verifyPhone: async ({ request, cookies }: import('./$types').RequestEvent) => {
    // Feature disabled
    return { success: false, error: 'Phone verification is currently disabled' };

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    const formData = await request.formData();
    const verificationCode = formData.get('verificationCode')?.toString() || '';

    // In a real implementation, you would verify the code against what was sent
    // For now, we'll just check if it's '123456' as a mock verification
    if (verificationCode !== '123456') {
      return fail(400, { verificationError: 'Invalid verification code' });
    }

    try {
      // Get existing preferences
      const preferences = (userData.preferences as any) || {};
      const securityPrefs = preferences.security || {};
      const phoneData = securityPrefs.phone || {};

      // Update the phone verification status
      const updatedPreferences = {
        ...preferences,
        security: {
          ...securityPrefs,
          phone: {
            ...phoneData,
            verified: true,
            verifiedAt: new Date().toISOString(),
          },
        },
      };

      // Update the user's preferences in the database
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          preferences: updatedPreferences,
        },
      });

      return { success: true, phoneVerified: true };
    } catch (error) {
      console.error('Error verifying phone number:', error);
      return fail(500, { error: 'Failed to verify phone number' });
    }
  },
  logoutSession: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const data = await request.formData();
    const sessionId = data.get('sessionId')?.toString();
    const token = cookies.get('auth_token');

    if (!sessionId) {
      return fail(400, { error: 'Session ID is required' });
    }

    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    try {
      // Get the session
      const session = await prisma.session.findUnique({
        where: { id: sessionId },
        include: { user: true },
      });

      if (!session) {
        return fail(404, { error: 'Session not found' });
      }

      // Verify the user owns this session
      const currentUser = await verifySessionToken(token);
      if (!currentUser || currentUser.id !== session.userId) {
        return fail(403, { error: 'Forbidden' });
      }

      // Check if trying to revoke current session
      const currentSession = await prisma.session.findFirst({
        where: {
          OR: [{ sessionToken: token }, { token: token }],
          isRevoked: false,
        },
      });

      if (currentSession && currentSession.id === sessionId) {
        return fail(400, { error: 'Cannot log out of current session using this method' });
      }

      // Revoke the session
      await prisma.session.update({
        where: { id: sessionId },
        data: { isRevoked: true },
      });

      console.log(`Session ${sessionId} revoked successfully`);
      return json({ type: 'success' });
    } catch (error) {
      console.error('Error logging out session:', error);
      return fail(500, { error: 'Failed to log out session' });
    }
  },

  // Log out all sessions except the current one
  logoutAllSessions: async ({ cookies, request }: import('./$types').RequestEvent) => {
    const token = cookies.get('auth_token');

    if (!token) {
      return fail(401, { error: 'Unauthorized' });
    }

    try {
      // Log the request content type for debugging
      console.log('Request content type:', request.headers.get('content-type'));

      const user = await verifySessionToken(token);
      if (!user) {
        return fail(401, { error: 'Unauthorized' });
      }

      // Clean up old sessions for this user
      try {
        await cleanupUserSessions(user.id);
        console.log('Cleaned up old sessions for user:', user.id);
      } catch (cleanupError) {
        console.error('Error cleaning up old sessions:', cleanupError);
        // Continue even if cleanup fails
      }

      // Find the current session
      const currentSession = await prisma.session.findFirst({
        where: {
          OR: [{ sessionToken: token }, { token: token }],
          isRevoked: false,
        },
      });

      if (!currentSession) {
        console.log('Current session not found, but proceeding with logout of other sessions');
      } else {
        console.log(`Current session found: ${currentSession.id}`);
      }

      // Revoke all other sessions
      const result = await prisma.session.updateMany({
        where: {
          userId: user.id,
          id: currentSession ? { not: currentSession.id } : undefined,
          isRevoked: false,
        },
        data: { isRevoked: true },
      });

      console.log(`Revoked ${result.count} other sessions`);
      return json({ type: 'success', count: result.count });
    } catch (error) {
      console.error('Error logging out all sessions:', error);
      return fail(500, { error: 'Failed to log out all sessions' });
    }
  },

  // Add passkey action
  addPasskey: async ({ request, cookies }: import('./$types').RequestEvent) => {
    const tokenData = await getUserFromToken(cookies);

    if (!tokenData || !tokenData.email) {
      return fail(401, { error: 'Unauthorized' });
    }

    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email },
    });

    if (!userData) {
      return fail(401, { error: 'Unauthorized' });
    }

    const form = await superValidate(request, zod(passkeysSchema));

    if (!form.valid) {
      return fail(400, { passkeysForm: form });
    }

    try {
      // This is just a placeholder action that will be intercepted by the client-side code
      // The actual passkey registration happens in the client using WebAuthn API
      return { passkeysForm: form };
    } catch (error) {
      console.error('Error adding passkey:', error);
      return fail(500, { passkeysForm: form, error: 'Failed to add passkey' });
    }
  },
};
;null as any as Actions;
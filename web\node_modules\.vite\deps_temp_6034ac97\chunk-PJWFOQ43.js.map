{"version": 3, "sources": ["browser-external:buffer", "../../safer-buffer/safer.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"buffer\" has been externalized for browser compatibility. Cannot access \"buffer.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/* eslint-disable node/no-deprecated-api */\n\n'use strict'\n\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\nvar safer = {}\n\nvar key\n\nfor (key in buffer) {\n  if (!buffer.hasOwnProperty(key)) continue\n  if (key === 'SlowBuffer' || key === 'Buffer') continue\n  safer[key] = buffer[key]\n}\n\nvar Safer = safer.Buffer = {}\nfor (key in Buffer) {\n  if (!Buffer.hasOwnProperty(key)) continue\n  if (key === 'allocUnsafe' || key === 'allocUnsafeSlow') continue\n  Safer[key] = Buffer[key]\n}\n\nsafer.Buffer.prototype = Buffer.prototype\n\nif (!Safer.from || Safer.from === Uint8Array.from) {\n  Safer.from = function (value, encodingOrOffset, length) {\n    if (typeof value === 'number') {\n      throw new TypeError('The \"value\" argument must not be of type number. Received type ' + typeof value)\n    }\n    if (value && typeof value.length === 'undefined') {\n      throw new TypeError('The first argument must be one of type string, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>y, or Array-like Object. Received type ' + typeof value)\n    }\n    return Buffer(value, encodingOrOffset, length)\n  }\n}\n\nif (!Safer.alloc) {\n  Safer.alloc = function (size, fill, encoding) {\n    if (typeof size !== 'number') {\n      throw new TypeError('The \"size\" argument must be of type number. Received type ' + typeof size)\n    }\n    if (size < 0 || size >= 2 * (1 << 30)) {\n      throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n    }\n    var buf = Buffer(size)\n    if (!fill || fill.length === 0) {\n      buf.fill(0)\n    } else if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n    return buf\n  }\n}\n\nif (!safer.kStringMaxLength) {\n  try {\n    safer.kStringMaxLength = process.binding('buffer').kStringMaxLength\n  } catch (e) {\n    // we can't determine kStringMaxLength in environments where process.binding\n    // is unsupported, so let's not set it\n  }\n}\n\nif (!safer.constants) {\n  safer.constants = {\n    MAX_LENGTH: safer.kMaxLength\n  }\n  if (safer.kStringMaxLength) {\n    safer.constants.MAX_STRING_LENGTH = safer.kStringMaxLength\n  }\n}\n\nmodule.exports = safer\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,mIAAmI;AAAA,QAC/O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAIA,QAAI,SAAS;AACb,QAAI,SAAS,OAAO;AAEpB,QAAI,QAAQ,CAAC;AAEb,QAAI;AAEJ,SAAK,OAAO,QAAQ;AAClB,UAAI,CAAC,OAAO,eAAe,GAAG,EAAG;AACjC,UAAI,QAAQ,gBAAgB,QAAQ,SAAU;AAC9C,YAAM,GAAG,IAAI,OAAO,GAAG;AAAA,IACzB;AAEA,QAAI,QAAQ,MAAM,SAAS,CAAC;AAC5B,SAAK,OAAO,QAAQ;AAClB,UAAI,CAAC,OAAO,eAAe,GAAG,EAAG;AACjC,UAAI,QAAQ,iBAAiB,QAAQ,kBAAmB;AACxD,YAAM,GAAG,IAAI,OAAO,GAAG;AAAA,IACzB;AAEA,UAAM,OAAO,YAAY,OAAO;AAEhC,QAAI,CAAC,MAAM,QAAQ,MAAM,SAAS,WAAW,MAAM;AACjD,YAAM,OAAO,SAAU,OAAO,kBAAkB,QAAQ;AACtD,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,IAAI,UAAU,oEAAoE,OAAO,KAAK;AAAA,QACtG;AACA,YAAI,SAAS,OAAO,MAAM,WAAW,aAAa;AAChD,gBAAM,IAAI,UAAU,oHAAoH,OAAO,KAAK;AAAA,QACtJ;AACA,eAAO,OAAO,OAAO,kBAAkB,MAAM;AAAA,MAC/C;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,OAAO;AAChB,YAAM,QAAQ,SAAU,MAAM,MAAM,UAAU;AAC5C,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,IAAI,UAAU,+DAA+D,OAAO,IAAI;AAAA,QAChG;AACA,YAAI,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK;AACrC,gBAAM,IAAI,WAAW,gBAAgB,OAAO,gCAAgC;AAAA,QAC9E;AACA,YAAI,MAAM,OAAO,IAAI;AACrB,YAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC9B,cAAI,KAAK,CAAC;AAAA,QACZ,WAAW,OAAO,aAAa,UAAU;AACvC,cAAI,KAAK,MAAM,QAAQ;AAAA,QACzB,OAAO;AACL,cAAI,KAAK,IAAI;AAAA,QACf;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,kBAAkB;AAC3B,UAAI;AACF,cAAM,mBAAmB,QAAQ,QAAQ,QAAQ,EAAE;AAAA,MACrD,SAAS,GAAG;AAAA,MAGZ;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,WAAW;AACpB,YAAM,YAAY;AAAA,QAChB,YAAY,MAAM;AAAA,MACpB;AACA,UAAI,MAAM,kBAAkB;AAC1B,cAAM,UAAU,oBAAoB,MAAM;AAAA,MAC5C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}
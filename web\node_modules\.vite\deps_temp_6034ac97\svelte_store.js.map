{"version": 3, "sources": ["../../svelte/src/store/index-client.js"], "sourcesContent": ["/** @import { Readable, Writable } from './public.js' */\nimport {\n\teffect_root,\n\teffect_tracking,\n\trender_effect\n} from '../internal/client/reactivity/effects.js';\nimport { get, writable } from './shared/index.js';\nimport { createSubscriber } from '../reactivity/create-subscriber.js';\nimport {\n\tactive_effect,\n\tactive_reaction,\n\tset_active_effect,\n\tset_active_reaction\n} from '../internal/client/runtime.js';\n\nexport { derived, get, readable, readonly, writable } from './shared/index.js';\n\n/**\n * @template V\n * @overload\n * @param {() => V} get\n * @param {(v: V) => void} set\n * @returns {Writable<V>}\n */\n/**\n * @template V\n * @overload\n * @param {() => V} get\n * @returns {Readable<V>}\n */\n/**\n * Create a store from a function that returns state, and (to make a writable store), an\n * optional second function that sets state.\n *\n * ```ts\n * import { toStore } from 'svelte/store';\n *\n * let count = $state(0);\n *\n * const store = toStore(() => count, (v) => (count = v));\n * ```\n * @template V\n * @param {() => V} get\n * @param {(v: V) => void} [set]\n * @returns {Writable<V> | Readable<V>}\n */\nexport function toStore(get, set) {\n\tvar effect = active_effect;\n\tvar reaction = active_reaction;\n\tvar init_value = get();\n\n\tconst store = writable(init_value, (set) => {\n\t\t// If the value has changed before we call subscribe, then\n\t\t// we need to treat the value as already having run\n\t\tvar ran = init_value !== get();\n\n\t\t// TODO do we need a different implementation on the server?\n\t\tvar teardown;\n\t\t// Apply the reaction and effect at the time of toStore being called\n\t\tvar previous_reaction = active_reaction;\n\t\tvar previous_effect = active_effect;\n\t\tset_active_reaction(reaction);\n\t\tset_active_effect(effect);\n\n\t\ttry {\n\t\t\tteardown = effect_root(() => {\n\t\t\t\trender_effect(() => {\n\t\t\t\t\tconst value = get();\n\t\t\t\t\tif (ran) set(value);\n\t\t\t\t});\n\t\t\t});\n\t\t} finally {\n\t\t\tset_active_reaction(previous_reaction);\n\t\t\tset_active_effect(previous_effect);\n\t\t}\n\n\t\tran = true;\n\n\t\treturn teardown;\n\t});\n\n\tif (set) {\n\t\treturn {\n\t\t\tset,\n\t\t\tupdate: (fn) => set(fn(get())),\n\t\t\tsubscribe: store.subscribe\n\t\t};\n\t}\n\n\treturn {\n\t\tsubscribe: store.subscribe\n\t};\n}\n\n/**\n * @template V\n * @overload\n * @param {Writable<V>} store\n * @returns {{ current: V }}\n */\n/**\n * @template V\n * @overload\n * @param {Readable<V>} store\n * @returns {{ readonly current: V }}\n */\n/**\n * Convert a store to an object with a reactive `current` property. If `store`\n * is a readable store, `current` will be a readonly property.\n *\n * ```ts\n * import { fromStore, get, writable } from 'svelte/store';\n *\n * const store = writable(0);\n *\n * const count = fromStore(store);\n *\n * count.current; // 0;\n * store.set(1);\n * count.current; // 1\n *\n * count.current += 1;\n * get(store); // 2\n * ```\n * @template V\n * @param {Writable<V> | Readable<V>} store\n */\nexport function fromStore(store) {\n\tlet value = /** @type {V} */ (undefined);\n\n\tconst subscribe = createSubscriber((update) => {\n\t\tlet ran = false;\n\n\t\tconst unsubscribe = store.subscribe((v) => {\n\t\t\tvalue = v;\n\t\t\tif (ran) update();\n\t\t});\n\n\t\tran = true;\n\n\t\treturn unsubscribe;\n\t});\n\n\tfunction current() {\n\t\tif (effect_tracking()) {\n\t\t\tsubscribe();\n\t\t\treturn value;\n\t\t}\n\n\t\treturn get(store);\n\t}\n\n\tif ('set' in store) {\n\t\treturn {\n\t\t\tget current() {\n\t\t\t\treturn current();\n\t\t\t},\n\t\t\tset current(v) {\n\t\t\t\tstore.set(v);\n\t\t\t}\n\t\t};\n\t}\n\n\treturn {\n\t\tget current() {\n\t\t\treturn current();\n\t\t}\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA8CO,SAAS,QAAQA,MAAK,KAAK;AACjC,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,aAAaA,KAAI;AAErB,QAAM,QAAQ,SAAS,YAAY,CAACC,SAAQ;AAG3C,QAAI,MAAM,eAAeD,KAAI;AAG7B,QAAI;AAEJ,QAAI,oBAAoB;AACxB,QAAI,kBAAkB;AACtB,wBAAoB,QAAQ;AAC5B,sBAAkB,MAAM;AAExB,QAAI;AACH,iBAAW,YAAY,MAAM;AAC5B,sBAAc,MAAM;AACnB,gBAAM,QAAQA,KAAI;AAClB,cAAI,IAAK,CAAAC,KAAI,KAAK;AAAA,QACnB,CAAC;AAAA,MACF,CAAC;AAAA,IACF,UAAE;AACD,0BAAoB,iBAAiB;AACrC,wBAAkB,eAAe;AAAA,IAClC;AAEA,UAAM;AAEN,WAAO;AAAA,EACR,CAAC;AAED,MAAI,KAAK;AACR,WAAO;AAAA,MACN;AAAA,MACA,QAAQ,CAAC,OAAO,IAAI,GAAGD,KAAI,CAAC,CAAC;AAAA,MAC7B,WAAW,MAAM;AAAA,IAClB;AAAA,EACD;AAEA,SAAO;AAAA,IACN,WAAW,MAAM;AAAA,EAClB;AACD;AAmCO,SAAS,UAAU,OAAO;AAChC,MAAI;AAAA;AAAA,IAA0B;AAAA;AAE9B,QAAM,YAAY,iBAAiB,CAAC,WAAW;AAC9C,QAAI,MAAM;AAEV,UAAM,cAAc,MAAM,UAAU,CAAC,MAAM;AAC1C,cAAQ;AACR,UAAI,IAAK,QAAO;AAAA,IACjB,CAAC;AAED,UAAM;AAEN,WAAO;AAAA,EACR,CAAC;AAED,WAAS,UAAU;AAClB,QAAI,gBAAgB,GAAG;AACtB,gBAAU;AACV,aAAO;AAAA,IACR;AAEA,WAAO,IAAI,KAAK;AAAA,EACjB;AAEA,MAAI,SAAS,OAAO;AACnB,WAAO;AAAA,MACN,IAAI,UAAU;AACb,eAAO,QAAQ;AAAA,MAChB;AAAA,MACA,IAAI,QAAQ,GAAG;AACd,cAAM,IAAI,CAAC;AAAA,MACZ;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN,IAAI,UAAU;AACb,aAAO,QAAQ;AAAA,IAChB;AAAA,EACD;AACD;", "names": ["get", "set"]}
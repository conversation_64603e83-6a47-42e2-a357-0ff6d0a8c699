{"version": 3, "sources": ["../../safe-buffer/index.js", "../../jws/lib/data-stream.js", "../../ecdsa-sig-formatter/src/param-bytes-for-alg.js", "../../ecdsa-sig-formatter/src/ecdsa-sig-formatter.js", "../../buffer-equal-constant-time/index.js", "../../jwa/index.js", "../../jws/lib/tostring.js", "../../jws/lib/sign-stream.js", "../../jws/lib/verify-stream.js", "../../jws/index.js", "../../web-push/src/web-push-constants.js", "../../web-push/src/urlsafe-base64-helper.js", "../../web-push/src/vapid-helper.js", "../../http_ece/ece.js", "../../web-push/src/encryption-helper.js", "../../web-push/src/web-push-error.js", "../../web-push/node_modules/agent-base/src/helpers.ts", "../../web-push/node_modules/agent-base/src/index.ts", "../../web-push/node_modules/https-proxy-agent/src/parse-proxy-response.ts", "../../web-push/node_modules/https-proxy-agent/src/index.ts", "../../web-push/src/web-push-lib.js", "../../web-push/src/index.js"], "sourcesContent": ["/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "/*global module, process*/\nvar Buffer = require('safe-buffer').Buffer;\nvar Stream = require('stream');\nvar util = require('util');\n\nfunction DataStream(data) {\n  this.buffer = null;\n  this.writable = true;\n  this.readable = true;\n\n  // No input\n  if (!data) {\n    this.buffer = Buffer.alloc(0);\n    return this;\n  }\n\n  // Stream\n  if (typeof data.pipe === 'function') {\n    this.buffer = Buffer.alloc(0);\n    data.pipe(this);\n    return this;\n  }\n\n  // Buffer or String\n  // or Object (assumedly a passworded key)\n  if (data.length || typeof data === 'object') {\n    this.buffer = data;\n    this.writable = false;\n    process.nextTick(function () {\n      this.emit('end', data);\n      this.readable = false;\n      this.emit('close');\n    }.bind(this));\n    return this;\n  }\n\n  throw new TypeError('Unexpected data type ('+ typeof data + ')');\n}\nutil.inherits(DataStream, Stream);\n\nDataStream.prototype.write = function write(data) {\n  this.buffer = Buffer.concat([this.buffer, Buffer.from(data)]);\n  this.emit('data', data);\n};\n\nDataStream.prototype.end = function end(data) {\n  if (data)\n    this.write(data);\n  this.emit('end', data);\n  this.emit('close');\n  this.writable = false;\n  this.readable = false;\n};\n\nmodule.exports = DataStream;\n", "'use strict';\n\nfunction getParamSize(keySize) {\n\tvar result = ((keySize / 8) | 0) + (keySize % 8 === 0 ? 0 : 1);\n\treturn result;\n}\n\nvar paramBytesForAlg = {\n\tES256: getParamSize(256),\n\tES384: getParamSize(384),\n\tES512: getParamSize(521)\n};\n\nfunction getParamBytesForAlg(alg) {\n\tvar paramBytes = paramBytesForAlg[alg];\n\tif (paramBytes) {\n\t\treturn paramBytes;\n\t}\n\n\tthrow new Error('Unknown algorithm \"' + alg + '\"');\n}\n\nmodule.exports = getParamBytesForAlg;\n", "'use strict';\n\nvar Buffer = require('safe-buffer').Buffer;\n\nvar getParamBytesForAlg = require('./param-bytes-for-alg');\n\nvar MAX_OCTET = 0x80,\n\tCLASS_UNIVERSAL = 0,\n\tPRIMITIVE_BIT = 0x20,\n\tTAG_SEQ = 0x10,\n\tTAG_INT = 0x02,\n\tENCODED_TAG_SEQ = (TAG_SEQ | PRIMITIVE_BIT) | (CLASS_UNIVERSAL << 6),\n\tENCODED_TAG_INT = TAG_INT | (CLASS_UNIVERSAL << 6);\n\nfunction base64Url(base64) {\n\treturn base64\n\t\t.replace(/=/g, '')\n\t\t.replace(/\\+/g, '-')\n\t\t.replace(/\\//g, '_');\n}\n\nfunction signatureAsBuffer(signature) {\n\tif (Buffer.isBuffer(signature)) {\n\t\treturn signature;\n\t} else if ('string' === typeof signature) {\n\t\treturn Buffer.from(signature, 'base64');\n\t}\n\n\tthrow new TypeError('ECDSA signature must be a Base64 string or a Buffer');\n}\n\nfunction derToJose(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\t// the DER encoded param should at most be the param size, plus a padding\n\t// zero, since due to being a signed integer\n\tvar maxEncodedParamLength = paramBytes + 1;\n\n\tvar inputLength = signature.length;\n\n\tvar offset = 0;\n\tif (signature[offset++] !== ENCODED_TAG_SEQ) {\n\t\tthrow new Error('Could not find expected \"seq\"');\n\t}\n\n\tvar seqLength = signature[offset++];\n\tif (seqLength === (MAX_OCTET | 1)) {\n\t\tseqLength = signature[offset++];\n\t}\n\n\tif (inputLength - offset < seqLength) {\n\t\tthrow new Error('\"seq\" specified length of \"' + seqLength + '\", only \"' + (inputLength - offset) + '\" remaining');\n\t}\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"r\"');\n\t}\n\n\tvar rLength = signature[offset++];\n\n\tif (inputLength - offset - 2 < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", only \"' + (inputLength - offset - 2) + '\" available');\n\t}\n\n\tif (maxEncodedParamLength < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar rOffset = offset;\n\toffset += rLength;\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"s\"');\n\t}\n\n\tvar sLength = signature[offset++];\n\n\tif (inputLength - offset !== sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", expected \"' + (inputLength - offset) + '\"');\n\t}\n\n\tif (maxEncodedParamLength < sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar sOffset = offset;\n\toffset += sLength;\n\n\tif (offset !== inputLength) {\n\t\tthrow new Error('Expected to consume entire buffer, but \"' + (inputLength - offset) + '\" bytes remain');\n\t}\n\n\tvar rPadding = paramBytes - rLength,\n\t\tsPadding = paramBytes - sLength;\n\n\tvar dst = Buffer.allocUnsafe(rPadding + rLength + sPadding + sLength);\n\n\tfor (offset = 0; offset < rPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, rOffset + Math.max(-rPadding, 0), rOffset + rLength);\n\n\toffset = paramBytes;\n\n\tfor (var o = offset; offset < o + sPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, sOffset + Math.max(-sPadding, 0), sOffset + sLength);\n\n\tdst = dst.toString('base64');\n\tdst = base64Url(dst);\n\n\treturn dst;\n}\n\nfunction countPadding(buf, start, stop) {\n\tvar padding = 0;\n\twhile (start + padding < stop && buf[start + padding] === 0) {\n\t\t++padding;\n\t}\n\n\tvar needsSign = buf[start + padding] >= MAX_OCTET;\n\tif (needsSign) {\n\t\t--padding;\n\t}\n\n\treturn padding;\n}\n\nfunction joseToDer(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\tvar signatureBytes = signature.length;\n\tif (signatureBytes !== paramBytes * 2) {\n\t\tthrow new TypeError('\"' + alg + '\" signatures must be \"' + paramBytes * 2 + '\" bytes, saw \"' + signatureBytes + '\"');\n\t}\n\n\tvar rPadding = countPadding(signature, 0, paramBytes);\n\tvar sPadding = countPadding(signature, paramBytes, signature.length);\n\tvar rLength = paramBytes - rPadding;\n\tvar sLength = paramBytes - sPadding;\n\n\tvar rsBytes = 1 + 1 + rLength + 1 + 1 + sLength;\n\n\tvar shortLength = rsBytes < MAX_OCTET;\n\n\tvar dst = Buffer.allocUnsafe((shortLength ? 2 : 3) + rsBytes);\n\n\tvar offset = 0;\n\tdst[offset++] = ENCODED_TAG_SEQ;\n\tif (shortLength) {\n\t\t// Bit 8 has value \"0\"\n\t\t// bits 7-1 give the length.\n\t\tdst[offset++] = rsBytes;\n\t} else {\n\t\t// Bit 8 of first octet has value \"1\"\n\t\t// bits 7-1 give the number of additional length octets.\n\t\tdst[offset++] = MAX_OCTET\t| 1;\n\t\t// length, base 256\n\t\tdst[offset++] = rsBytes & 0xff;\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = rLength;\n\tif (rPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\toffset += signature.copy(dst, offset, 0, paramBytes);\n\t} else {\n\t\toffset += signature.copy(dst, offset, rPadding, paramBytes);\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = sLength;\n\tif (sPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\tsignature.copy(dst, offset, paramBytes);\n\t} else {\n\t\tsignature.copy(dst, offset, paramBytes + sPadding);\n\t}\n\n\treturn dst;\n}\n\nmodule.exports = {\n\tderToJose: derToJose,\n\tjoseToDer: joseToDer\n};\n", "/*jshint node:true */\n'use strict';\nvar Buffer = require('buffer').Buffer; // browserify\nvar SlowBuffer = require('buffer').SlowBuffer;\n\nmodule.exports = bufferEq;\n\nfunction bufferEq(a, b) {\n\n  // shortcutting on type is necessary for correctness\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    return false;\n  }\n\n  // buffer sizes should be well-known information, so despite this\n  // shortcutting, it doesn't leak any information about the *contents* of the\n  // buffers.\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  var c = 0;\n  for (var i = 0; i < a.length; i++) {\n    /*jshint bitwise:false */\n    c |= a[i] ^ b[i]; // XOR\n  }\n  return c === 0;\n}\n\nbufferEq.install = function() {\n  Buffer.prototype.equal = SlowBuffer.prototype.equal = function equal(that) {\n    return bufferEq(this, that);\n  };\n};\n\nvar origBufEqual = Buffer.prototype.equal;\nvar origSlowBufEqual = SlowBuffer.prototype.equal;\nbufferEq.restore = function() {\n  Buffer.prototype.equal = origBufEqual;\n  SlowBuffer.prototype.equal = origSlowBufEqual;\n};\n", "var Buffer = require('safe-buffer').Buffer;\nvar crypto = require('crypto');\nvar formatEcdsa = require('ecdsa-sig-formatter');\nvar util = require('util');\n\nvar MSG_INVALID_ALGORITHM = '\"%s\" is not a valid algorithm.\\n  Supported algorithms are:\\n  \"HS256\", \"HS384\", \"HS512\", \"RS256\", \"RS384\", \"RS512\", \"PS256\", \"PS384\", \"PS512\", \"ES256\", \"ES384\", \"ES512\" and \"none\".'\nvar MSG_INVALID_SECRET = 'secret must be a string or buffer';\nvar MSG_INVALID_VERIFIER_KEY = 'key must be a string or a buffer';\nvar MSG_INVALID_SIGNER_KEY = 'key must be a string, a buffer or an object';\n\nvar supportsKeyObjects = typeof crypto.createPublicKey === 'function';\nif (supportsKeyObjects) {\n  MSG_INVALID_VERIFIER_KEY += ' or a KeyObject';\n  MSG_INVALID_SECRET += 'or a KeyObject';\n}\n\nfunction checkIsPublicKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.type !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.asymmetricKeyType !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n};\n\nfunction checkIsPrivateKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (typeof key === 'object') {\n    return;\n  }\n\n  throw typeError(MSG_INVALID_SIGNER_KEY);\n};\n\nfunction checkIsSecretKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return key;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (key.type !== 'secret') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n}\n\nfunction fromBase64(base64) {\n  return base64\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction toBase64(base64url) {\n  base64url = base64url.toString();\n\n  var padding = 4 - base64url.length % 4;\n  if (padding !== 4) {\n    for (var i = 0; i < padding; ++i) {\n      base64url += '=';\n    }\n  }\n\n  return base64url\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n}\n\nfunction typeError(template) {\n  var args = [].slice.call(arguments, 1);\n  var errMsg = util.format.bind(util, template).apply(null, args);\n  return new TypeError(errMsg);\n}\n\nfunction bufferOrString(obj) {\n  return Buffer.isBuffer(obj) || typeof obj === 'string';\n}\n\nfunction normalizeInput(thing) {\n  if (!bufferOrString(thing))\n    thing = JSON.stringify(thing);\n  return thing;\n}\n\nfunction createHmacSigner(bits) {\n  return function sign(thing, secret) {\n    checkIsSecretKey(secret);\n    thing = normalizeInput(thing);\n    var hmac = crypto.createHmac('sha' + bits, secret);\n    var sig = (hmac.update(thing), hmac.digest('base64'))\n    return fromBase64(sig);\n  }\n}\n\nvar bufferEqual;\nvar timingSafeEqual = 'timingSafeEqual' in crypto ? function timingSafeEqual(a, b) {\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n\n  return crypto.timingSafeEqual(a, b)\n} : function timingSafeEqual(a, b) {\n  if (!bufferEqual) {\n    bufferEqual = require('buffer-equal-constant-time');\n  }\n\n  return bufferEqual(a, b)\n}\n\nfunction createHmacVerifier(bits) {\n  return function verify(thing, signature, secret) {\n    var computedSig = createHmacSigner(bits)(thing, secret);\n    return timingSafeEqual(Buffer.from(signature), Buffer.from(computedSig));\n  }\n}\n\nfunction createKeySigner(bits) {\n return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    // Even though we are specifying \"RSA\" here, this works with ECDSA\n    // keys as well.\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign(privateKey, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify(publicKey, signature, 'base64');\n  }\n}\n\nfunction createPSSKeySigner(bits) {\n  return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign({\n      key: privateKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createPSSKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify({\n      key: publicKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, signature, 'base64');\n  }\n}\n\nfunction createECDSASigner(bits) {\n  var inner = createKeySigner(bits);\n  return function sign() {\n    var signature = inner.apply(null, arguments);\n    signature = formatEcdsa.derToJose(signature, 'ES' + bits);\n    return signature;\n  };\n}\n\nfunction createECDSAVerifer(bits) {\n  var inner = createKeyVerifier(bits);\n  return function verify(thing, signature, publicKey) {\n    signature = formatEcdsa.joseToDer(signature, 'ES' + bits).toString('base64');\n    var result = inner(thing, signature, publicKey);\n    return result;\n  };\n}\n\nfunction createNoneSigner() {\n  return function sign() {\n    return '';\n  }\n}\n\nfunction createNoneVerifier() {\n  return function verify(thing, signature) {\n    return signature === '';\n  }\n}\n\nmodule.exports = function jwa(algorithm) {\n  var signerFactories = {\n    hs: createHmacSigner,\n    rs: createKeySigner,\n    ps: createPSSKeySigner,\n    es: createECDSASigner,\n    none: createNoneSigner,\n  }\n  var verifierFactories = {\n    hs: createHmacVerifier,\n    rs: createKeyVerifier,\n    ps: createPSSKeyVerifier,\n    es: createECDSAVerifer,\n    none: createNoneVerifier,\n  }\n  var match = algorithm.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/);\n  if (!match)\n    throw typeError(MSG_INVALID_ALGORITHM, algorithm);\n  var algo = (match[1] || match[3]).toLowerCase();\n  var bits = match[2];\n\n  return {\n    sign: signerFactories[algo](bits),\n    verify: verifierFactories[algo](bits),\n  }\n};\n", "/*global module*/\nvar Buffer = require('buffer').Buffer;\n\nmodule.exports = function toString(obj) {\n  if (typeof obj === 'string')\n    return obj;\n  if (typeof obj === 'number' || Buffer.isBuffer(obj))\n    return obj.toString();\n  return JSON.stringify(obj);\n};\n", "/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\n\nfunction base64url(string, encoding) {\n  return Buffer\n    .from(string, encoding)\n    .toString('base64')\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction jwsSecuredInput(header, payload, encoding) {\n  encoding = encoding || 'utf8';\n  var encodedHeader = base64url(toString(header), 'binary');\n  var encodedPayload = base64url(toString(payload), encoding);\n  return util.format('%s.%s', encodedHeader, encodedPayload);\n}\n\nfunction jwsSign(opts) {\n  var header = opts.header;\n  var payload = opts.payload;\n  var secretOrKey = opts.secret || opts.privateKey;\n  var encoding = opts.encoding;\n  var algo = jwa(header.alg);\n  var securedInput = jwsSecuredInput(header, payload, encoding);\n  var signature = algo.sign(securedInput, secretOrKey);\n  return util.format('%s.%s', securedInput, signature);\n}\n\nfunction SignStream(opts) {\n  var secret = opts.secret||opts.privateKey||opts.key;\n  var secretStream = new DataStream(secret);\n  this.readable = true;\n  this.header = opts.header;\n  this.encoding = opts.encoding;\n  this.secret = this.privateKey = this.key = secretStream;\n  this.payload = new DataStream(opts.payload);\n  this.secret.once('close', function () {\n    if (!this.payload.writable && this.readable)\n      this.sign();\n  }.bind(this));\n\n  this.payload.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.sign();\n  }.bind(this));\n}\nutil.inherits(SignStream, Stream);\n\nSignStream.prototype.sign = function sign() {\n  try {\n    var signature = jwsSign({\n      header: this.header,\n      payload: this.payload.buffer,\n      secret: this.secret.buffer,\n      encoding: this.encoding\n    });\n    this.emit('done', signature);\n    this.emit('data', signature);\n    this.emit('end');\n    this.readable = false;\n    return signature;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nSignStream.sign = jwsSign;\n\nmodule.exports = SignStream;\n", "/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\nvar JWS_REGEX = /^[a-zA-Z0-9\\-_]+?\\.[a-zA-Z0-9\\-_]+?\\.([a-zA-Z0-9\\-_]+)?$/;\n\nfunction isObject(thing) {\n  return Object.prototype.toString.call(thing) === '[object Object]';\n}\n\nfunction safeJsonParse(thing) {\n  if (isObject(thing))\n    return thing;\n  try { return JSON.parse(thing); }\n  catch (e) { return undefined; }\n}\n\nfunction headerFromJWS(jwsSig) {\n  var encodedHeader = jwsSig.split('.', 1)[0];\n  return safeJsonParse(Buffer.from(encodedHeader, 'base64').toString('binary'));\n}\n\nfunction securedInputFromJWS(jwsSig) {\n  return jwsSig.split('.', 2).join('.');\n}\n\nfunction signatureFromJWS(jwsSig) {\n  return jwsSig.split('.')[2];\n}\n\nfunction payloadFromJWS(jwsSig, encoding) {\n  encoding = encoding || 'utf8';\n  var payload = jwsSig.split('.')[1];\n  return Buffer.from(payload, 'base64').toString(encoding);\n}\n\nfunction isValidJws(string) {\n  return JWS_REGEX.test(string) && !!headerFromJWS(string);\n}\n\nfunction jwsVerify(jwsSig, algorithm, secretOrKey) {\n  if (!algorithm) {\n    var err = new Error(\"Missing algorithm parameter for jws.verify\");\n    err.code = \"MISSING_ALGORITHM\";\n    throw err;\n  }\n  jwsSig = toString(jwsSig);\n  var signature = signatureFromJWS(jwsSig);\n  var securedInput = securedInputFromJWS(jwsSig);\n  var algo = jwa(algorithm);\n  return algo.verify(securedInput, signature, secretOrKey);\n}\n\nfunction jwsDecode(jwsSig, opts) {\n  opts = opts || {};\n  jwsSig = toString(jwsSig);\n\n  if (!isValidJws(jwsSig))\n    return null;\n\n  var header = headerFromJWS(jwsSig);\n\n  if (!header)\n    return null;\n\n  var payload = payloadFromJWS(jwsSig);\n  if (header.typ === 'JWT' || opts.json)\n    payload = JSON.parse(payload, opts.encoding);\n\n  return {\n    header: header,\n    payload: payload,\n    signature: signatureFromJWS(jwsSig)\n  };\n}\n\nfunction VerifyStream(opts) {\n  opts = opts || {};\n  var secretOrKey = opts.secret||opts.publicKey||opts.key;\n  var secretStream = new DataStream(secretOrKey);\n  this.readable = true;\n  this.algorithm = opts.algorithm;\n  this.encoding = opts.encoding;\n  this.secret = this.publicKey = this.key = secretStream;\n  this.signature = new DataStream(opts.signature);\n  this.secret.once('close', function () {\n    if (!this.signature.writable && this.readable)\n      this.verify();\n  }.bind(this));\n\n  this.signature.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.verify();\n  }.bind(this));\n}\nutil.inherits(VerifyStream, Stream);\nVerifyStream.prototype.verify = function verify() {\n  try {\n    var valid = jwsVerify(this.signature.buffer, this.algorithm, this.key.buffer);\n    var obj = jwsDecode(this.signature.buffer, this.encoding);\n    this.emit('done', valid, obj);\n    this.emit('data', valid);\n    this.emit('end');\n    this.readable = false;\n    return valid;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nVerifyStream.decode = jwsDecode;\nVerifyStream.isValid = isValidJws;\nVerifyStream.verify = jwsVerify;\n\nmodule.exports = VerifyStream;\n", "/*global exports*/\nvar SignStream = require('./lib/sign-stream');\nvar VerifyStream = require('./lib/verify-stream');\n\nvar ALGORITHMS = [\n  'HS256', 'HS384', 'HS512',\n  'RS256', 'RS384', 'RS512',\n  'PS256', 'PS384', 'PS512',\n  'ES256', 'ES384', 'ES512'\n];\n\nexports.ALGORITHMS = ALGORITHMS;\nexports.sign = SignStream.sign;\nexports.verify = VerifyStream.verify;\nexports.decode = VerifyStream.decode;\nexports.isValid = VerifyStream.isValid;\nexports.createSign = function createSign(opts) {\n  return new SignStream(opts);\n};\nexports.createVerify = function createVerify(opts) {\n  return new VerifyStream(opts);\n};\n", "'use strict';\n\nconst WebPushConstants = {};\n\nWebPushConstants.supportedContentEncodings = {\n  AES_GCM: 'aesgcm',\n  AES_128_GCM: 'aes128gcm'\n};\n\nWebPushConstants.supportedUrgency = {\n  VERY_LOW: 'very-low',\n  LOW: 'low',\n  NORMAL: 'normal',\n  HIGH: 'high'\n};\n\nmodule.exports = WebPushConstants;\n", "'use strict';\n\n/**\n * @param {string} base64\n * @returns {boolean}\n */\nfunction validate(base64) {\n  return /^[A-Za-z0-9\\-_]+$/.test(base64);\n}\n\nmodule.exports = {\n  validate: validate\n};\n", "'use strict';\n\nconst crypto = require('crypto');\nconst asn1 = require('asn1.js');\nconst jws = require('jws');\nconst { URL } = require('url');\n\nconst WebPushConstants = require('./web-push-constants.js');\nconst urlBase64Helper = require('./urlsafe-base64-helper');\n\n/**\n * DEFAULT_EXPIRATION is set to seconds in 12 hours\n */\nconst DEFAULT_EXPIRATION_SECONDS = 12 * 60 * 60;\n\n// Maximum expiration is 24 hours according. (See VAPID spec)\nconst MAX_EXPIRATION_SECONDS = 24 * 60 * 60;\n\nconst ECPrivateKeyASN = asn1.define('ECPrivateKey', function() {\n  this.seq().obj(\n    this.key('version').int(),\n    this.key('privateKey').octstr(),\n    this.key('parameters').explicit(0).objid()\n      .optional(),\n    this.key('publicKey').explicit(1).bitstr()\n      .optional()\n  );\n});\n\nfunction toPEM(key) {\n  return ECPrivateKeyASN.encode({\n    version: 1,\n    privateKey: key,\n    parameters: [1, 2, 840, 10045, 3, 1, 7] // prime256v1\n  }, 'pem', {\n    label: 'EC PRIVATE KEY'\n  });\n}\n\nfunction generateVAPIDKeys() {\n  const curve = crypto.createECDH('prime256v1');\n  curve.generateKeys();\n\n  let publicKeyBuffer = curve.getPublicKey();\n  let privateKeyBuffer = curve.getPrivateKey();\n\n  // Occassionally the keys will not be padded to the correct lengh resulting\n  // in errors, hence this padding.\n  // See https://github.com/web-push-libs/web-push/issues/295 for history.\n  if (privateKeyBuffer.length < 32) {\n    const padding = Buffer.alloc(32 - privateKeyBuffer.length);\n    padding.fill(0);\n    privateKeyBuffer = Buffer.concat([padding, privateKeyBuffer]);\n  }\n\n  if (publicKeyBuffer.length < 65) {\n    const padding = Buffer.alloc(65 - publicKeyBuffer.length);\n    padding.fill(0);\n    publicKeyBuffer = Buffer.concat([padding, publicKeyBuffer]);\n  }\n\n  return {\n    publicKey: publicKeyBuffer.toString('base64url'),\n    privateKey: privateKeyBuffer.toString('base64url')\n  };\n}\n\nfunction validateSubject(subject) {\n  if (!subject) {\n    throw new Error('No subject set in vapidDetails.subject.');\n  }\n\n  if (typeof subject !== 'string' || subject.length === 0) {\n    throw new Error('The subject value must be a string containing an https: URL or '\n    + 'mailto: address. ' + subject);\n  }\n\n  let subjectParseResult = null;\n  try {\n    subjectParseResult = new URL(subject);\n  } catch (err) {\n    throw new Error('Vapid subject is not a valid URL. ' + subject);\n  }\n  if (!['https:', 'mailto:'].includes(subjectParseResult.protocol)) {\n    throw new Error('Vapid subject is not an https: or mailto: URL. ' + subject);\n  }\n  if (subjectParseResult.hostname === 'localhost') {\n    console.warn('Vapid subject points to a localhost web URI, which is unsupported by '\n      + 'Apple\\'s push notification server and will result in a BadJwtToken error when '\n      + 'sending notifications.');\n    }\n}\n\nfunction validatePublicKey(publicKey) {\n  if (!publicKey) {\n    throw new Error('No key set vapidDetails.publicKey');\n  }\n\n  if (typeof publicKey !== 'string') {\n    throw new Error('Vapid public key is must be a URL safe Base 64 '\n    + 'encoded string.');\n  }\n\n  if (!urlBase64Helper.validate(publicKey)) {\n    throw new Error('Vapid public key must be a URL safe Base 64 (without \"=\")');\n  }\n\n  publicKey = Buffer.from(publicKey, 'base64url');\n\n  if (publicKey.length !== 65) {\n    throw new Error('Vapid public key should be 65 bytes long when decoded.');\n  }\n}\n\nfunction validatePrivateKey(privateKey) {\n  if (!privateKey) {\n    throw new Error('No key set in vapidDetails.privateKey');\n  }\n\n  if (typeof privateKey !== 'string') {\n    throw new Error('Vapid private key must be a URL safe Base 64 '\n    + 'encoded string.');\n  }\n\n  if (!urlBase64Helper.validate(privateKey)) {\n    throw new Error('Vapid private key must be a URL safe Base 64 (without \"=\")');\n  }\n\n  privateKey = Buffer.from(privateKey, 'base64url');\n\n  if (privateKey.length !== 32) {\n    throw new Error('Vapid private key should be 32 bytes long when decoded.');\n  }\n}\n\n/**\n * Given the number of seconds calculates\n * the expiration in the future by adding the passed `numSeconds`\n * with the current seconds from Unix Epoch\n *\n * @param {Number} numSeconds Number of seconds to be added\n * @return {Number} Future expiration in seconds\n */\nfunction getFutureExpirationTimestamp(numSeconds) {\n  const futureExp = new Date();\n  futureExp.setSeconds(futureExp.getSeconds() + numSeconds);\n  return Math.floor(futureExp.getTime() / 1000);\n}\n\n/**\n * Validates the Expiration Header based on the VAPID Spec\n * Throws error of type `Error` if the expiration is not validated\n *\n * @param {Number} expiration Expiration seconds from Epoch to be validated\n */\nfunction validateExpiration(expiration) {\n  if (!Number.isInteger(expiration)) {\n    throw new Error('`expiration` value must be a number');\n  }\n\n  if (expiration < 0) {\n    throw new Error('`expiration` must be a positive integer');\n  }\n\n  // Roughly checks the time of expiration, since the max expiration can be ahead\n  // of the time than at the moment the expiration was generated\n  const maxExpirationTimestamp = getFutureExpirationTimestamp(MAX_EXPIRATION_SECONDS);\n\n  if (expiration >= maxExpirationTimestamp) {\n    throw new Error('`expiration` value is greater than maximum of 24 hours');\n  }\n}\n\n/**\n * This method takes the required VAPID parameters and returns the required\n * header to be added to a Web Push Protocol Request.\n * @param  {string} audience        This must be the origin of the push service.\n * @param  {string} subject         This should be a URL or a 'mailto:' email\n * address.\n * @param  {string} publicKey       The VAPID public key.\n * @param  {string} privateKey      The VAPID private key.\n * @param  {string} contentEncoding The contentEncoding type.\n * @param  {integer} [expiration]   The expiration of the VAPID JWT.\n * @return {Object}                 Returns an Object with the Authorization and\n * 'Crypto-Key' values to be used as headers.\n */\nfunction getVapidHeaders(audience, subject, publicKey, privateKey, contentEncoding, expiration) {\n  if (!audience) {\n    throw new Error('No audience could be generated for VAPID.');\n  }\n\n  if (typeof audience !== 'string' || audience.length === 0) {\n    throw new Error('The audience value must be a string containing the '\n    + 'origin of a push service. ' + audience);\n  }\n\n  try {\n    new URL(audience); // eslint-disable-line no-new\n  } catch (err) {\n    throw new Error('VAPID audience is not a url. ' + audience);\n  }\n\n  validateSubject(subject);\n  validatePublicKey(publicKey);\n  validatePrivateKey(privateKey);\n\n  privateKey = Buffer.from(privateKey, 'base64url');\n\n  if (expiration) {\n    validateExpiration(expiration);\n  } else {\n    expiration = getFutureExpirationTimestamp(DEFAULT_EXPIRATION_SECONDS);\n  }\n\n  const header = {\n    typ: 'JWT',\n    alg: 'ES256'\n  };\n\n  const jwtPayload = {\n    aud: audience,\n    exp: expiration,\n    sub: subject\n  };\n\n  const jwt = jws.sign({\n    header: header,\n    payload: jwtPayload,\n    privateKey: toPEM(privateKey)\n  });\n\n  if (contentEncoding === WebPushConstants.supportedContentEncodings.AES_128_GCM) {\n    return {\n      Authorization: 'vapid t=' + jwt + ', k=' + publicKey\n    };\n  }\n  if (contentEncoding === WebPushConstants.supportedContentEncodings.AES_GCM) {\n    return {\n      Authorization: 'WebPush ' + jwt,\n      'Crypto-Key': 'p256ecdsa=' + publicKey\n    };\n  }\n\n  throw new Error('Unsupported encoding type specified.');\n}\n\nmodule.exports = {\n  generateVAPIDKeys: generateVAPIDKeys,\n  getFutureExpirationTimestamp: getFutureExpirationTimestamp,\n  getVapidHeaders: getVapidHeaders,\n  validateSubject: validateSubject,\n  validatePublicKey: validatePublicKey,\n  validatePrivateKey: validatePrivateKey,\n  validateExpiration: validateExpiration\n};\n", "'use strict';\n/*\n * Encrypted content coding\n *\n * === Note about versions ===\n *\n * This code supports multiple versions of the draft.  This is selected using\n * the |version| parameter.\n *\n * aes128gcm: The most recent version, the salt, record size and key identifier\n *    are included in a header that is part of the encrypted content coding.\n *\n * aesgcm: The version that is widely deployed with WebPush (as of 2016-11).\n *    This version is selected by default, unless you specify a |padSize| of 1.\n */\n\nvar crypto = require('crypto');\n\nvar AES_GCM = 'aes-128-gcm';\nvar PAD_SIZE = { 'aes128gcm': 1, 'aesgcm': 2 };\nvar TAG_LENGTH = 16;\nvar KEY_LENGTH = 16;\nvar NONCE_LENGTH = 12;\nvar SHA_256_LENGTH = 32;\nvar MODE_ENCRYPT = 'encrypt';\nvar MODE_DECRYPT = 'decrypt';\n\nvar keylog;\nif (process.env.ECE_KEYLOG === '1') {\n  keylog = function(m, k) {\n    console.warn(m + ' [' + k.length + ']: ' + k.toString('base64url'));\n    return k;\n  };\n} else {\n  keylog = function(m, k) { return k; };\n}\n\n/* Optionally base64 decode something. */\nfunction decode(b) {\n  if (typeof b === 'string') {\n    return Buffer.from(b, 'base64url');\n  }\n  return b;\n}\n\nfunction HMAC_hash(key, input) {\n  var hmac = crypto.createHmac('sha256', key);\n  hmac.update(input);\n  return hmac.digest();\n}\n\n/* HKDF as defined in RFC5869, using SHA-256 */\nfunction HKDF_extract(salt, ikm) {\n  keylog('salt', salt);\n  keylog('ikm', ikm);\n  return keylog('extract', HMAC_hash(salt, ikm));\n}\n\nfunction HKDF_expand(prk, info, l) {\n  keylog('prk', prk);\n  keylog('info', info);\n  var output = Buffer.alloc(0);\n  var T = Buffer.alloc(0);\n  info = Buffer.from(info, 'ascii');\n  var counter = 0;\n  var cbuf = Buffer.alloc(1);\n  while (output.length < l) {\n    cbuf.writeUIntBE(++counter, 0, 1);\n    T = HMAC_hash(prk, Buffer.concat([T, info, cbuf]));\n    output = Buffer.concat([output, T]);\n  }\n\n  return keylog('expand', output.slice(0, l));\n}\n\nfunction HKDF(salt, ikm, info, len) {\n  return HKDF_expand(HKDF_extract(salt, ikm), info, len);\n}\n\nfunction info(base, context) {\n  var result = Buffer.concat([\n    Buffer.from('Content-Encoding: ' + base + '\\0', 'ascii'),\n    context\n  ]);\n  keylog('info ' + base, result);\n  return result;\n}\n\nfunction lengthPrefix(buffer) {\n  var b = Buffer.concat([Buffer.alloc(2), buffer]);\n  b.writeUIntBE(buffer.length, 0, 2);\n  return b;\n}\n\nfunction extractDH(header, mode) {\n  var key = header.privateKey;\n  var senderPubKey, receiverPubKey;\n  if (mode === MODE_ENCRYPT) {\n    senderPubKey = key.getPublicKey();\n    receiverPubKey = header.dh;\n  } else if (mode === MODE_DECRYPT) {\n    senderPubKey = header.dh;\n    receiverPubKey = key.getPublicKey();\n  } else {\n    throw new Error('Unknown mode only ' + MODE_ENCRYPT +\n                    ' and ' + MODE_DECRYPT + ' supported');\n  }\n  return {\n    secret: key.computeSecret(header.dh),\n    context: Buffer.concat([\n      Buffer.from(header.keylabel, 'ascii'),\n      Buffer.from([0]),\n      lengthPrefix(receiverPubKey), // user agent\n      lengthPrefix(senderPubKey)    // application server\n    ])\n  };\n}\n\nfunction extractSecretAndContext(header, mode) {\n  var result = { secret: null, context: Buffer.alloc(0) };\n  if (header.key) {\n    result.secret = header.key;\n    if (result.secret.length !== KEY_LENGTH) {\n      throw new Error('An explicit key must be ' + KEY_LENGTH + ' bytes');\n    }\n  } else if (header.dh) { // receiver/decrypt\n    result = extractDH(header, mode);\n  } else if (typeof header.keyid !== undefined) {\n    result.secret = header.keymap[header.keyid];\n  }\n  if (!result.secret) {\n    throw new Error('Unable to determine key');\n  }\n  keylog('secret', result.secret);\n  keylog('context', result.context);\n  if (header.authSecret) {\n    result.secret = HKDF(header.authSecret, result.secret,\n                         info('auth', Buffer.alloc(0)), SHA_256_LENGTH);\n    keylog('authsecret', result.secret);\n  }\n  return result;\n}\n\nfunction webpushSecret(header, mode) {\n  if (!header.authSecret) {\n    throw new Error('No authentication secret for webpush');\n  }\n  keylog('authsecret', header.authSecret);\n\n  var remotePubKey, senderPubKey, receiverPubKey;\n  if (mode === MODE_ENCRYPT) {\n    senderPubKey = header.privateKey.getPublicKey();\n    remotePubKey = receiverPubKey = header.dh;\n  } else if (mode === MODE_DECRYPT) {\n    remotePubKey = senderPubKey = header.keyid;\n    receiverPubKey = header.privateKey.getPublicKey();\n  } else {\n    throw new Error('Unknown mode only ' + MODE_ENCRYPT +\n                    ' and ' + MODE_DECRYPT + ' supported');\n  }\n  keylog('remote pubkey', remotePubKey);\n  keylog('sender pubkey', senderPubKey);\n  keylog('receiver pubkey', receiverPubKey);\n  return keylog('secret dh',\n                HKDF(header.authSecret,\n                     header.privateKey.computeSecret(remotePubKey),\n                     Buffer.concat([\n                       Buffer.from('WebPush: info\\0'),\n                       receiverPubKey,\n                       senderPubKey\n                     ]),\n                     SHA_256_LENGTH));\n}\n\nfunction extractSecret(header, mode, keyLookupCallback) {\n  if (keyLookupCallback) {\n    if (!isFunction(keyLookupCallback)) {\n      throw new Error('Callback is not a function')\n    }\n  }\n\n  if (header.key) {\n    if (header.key.length !== KEY_LENGTH) {\n      throw new Error('An explicit key must be ' + KEY_LENGTH + ' bytes');\n    }\n    return keylog('secret key', header.key);\n  }\n\n  if (!header.privateKey) {\n    // Lookup based on keyid\n    if (!keyLookupCallback) {\n      var key = header.keymap && header.keymap[header.keyid];\n    } else {\n      var key = keyLookupCallback(header.keyid)\n    }\n    if (!key) {\n      throw new Error('No saved key (keyid: \"' + header.keyid + '\")');\n    }\n    return key;\n  }\n\n  return webpushSecret(header, mode);\n}\n\nfunction deriveKeyAndNonce(header, mode, lookupKeyCallback) {\n  if (!header.salt) {\n    throw new Error('must include a salt parameter for ' + header.version);\n  }\n  var keyInfo;\n  var nonceInfo;\n  var secret;\n  if (header.version === 'aesgcm') {\n    // old\n    var s = extractSecretAndContext(header, mode, lookupKeyCallback);\n    keyInfo = info('aesgcm', s.context);\n    nonceInfo = info('nonce', s.context);\n    secret = s.secret;\n  } else if (header.version === 'aes128gcm') {\n    // latest\n    keyInfo = Buffer.from('Content-Encoding: aes128gcm\\0');\n    nonceInfo = Buffer.from('Content-Encoding: nonce\\0');\n    secret = extractSecret(header, mode, lookupKeyCallback);\n  } else {\n    throw new Error('Unable to set context for mode ' + header.version);\n  }\n  var prk = HKDF_extract(header.salt, secret);\n  var result = {\n    key: HKDF_expand(prk, keyInfo, KEY_LENGTH),\n    nonce: HKDF_expand(prk, nonceInfo, NONCE_LENGTH)\n  };\n  keylog('key', result.key);\n  keylog('nonce base', result.nonce);\n  return result;\n}\n\n/* Parse command-line arguments. */\nfunction parseParams(params) {\n  var header = {};\n\n  header.version = params.version || 'aes128gcm';\n  header.rs = parseInt(params.rs, 10);\n  if (isNaN(header.rs)) {\n    header.rs = 4096;\n  }\n  var overhead = PAD_SIZE[header.version];\n  if (header.version === 'aes128gcm') {\n    overhead += TAG_LENGTH;\n  }\n  if (header.rs <= overhead) {\n    throw new Error('The rs parameter has to be greater than ' + overhead);\n  }\n\n  if (params.salt) {\n    header.salt = decode(params.salt);\n    if (header.salt.length !== KEY_LENGTH) {\n      throw new Error('The salt parameter must be ' + KEY_LENGTH + ' bytes');\n    }\n  }\n  header.keyid = params.keyid;\n  if (params.key) {\n    header.key = decode(params.key);\n  } else {\n    header.privateKey = params.privateKey;\n    if (!header.privateKey) {\n      header.keymap = params.keymap;\n    }\n    if (header.version !== 'aes128gcm') {\n      header.keylabel = params.keylabel || 'P-256';\n    }\n    if (params.dh) {\n      header.dh = decode(params.dh);\n    }\n  }\n  if (params.authSecret) {\n    header.authSecret = decode(params.authSecret);\n  }\n  return header;\n}\n\nfunction generateNonce(base, counter) {\n  var nonce = Buffer.from(base);\n  var m = nonce.readUIntBE(nonce.length - 6, 6);\n  var x = ((m ^ counter) & 0xffffff) +\n      ((((m / 0x1000000) ^ (counter / 0x1000000)) & 0xffffff) * 0x1000000);\n  nonce.writeUIntBE(x, nonce.length - 6, 6);\n  keylog('nonce' + counter, nonce);\n  return nonce;\n}\n\n/* Used when decrypting aes128gcm to populate the header values. Modifies the\n * header values in place and returns the size of the header. */\nfunction readHeader(buffer, header) {\n  var idsz = buffer.readUIntBE(20, 1);\n  header.salt = buffer.slice(0, KEY_LENGTH);\n  header.rs = buffer.readUIntBE(KEY_LENGTH, 4);\n  header.keyid = buffer.slice(21, 21 + idsz);\n  return 21 + idsz;\n}\n\nfunction unpadLegacy(data, version) {\n  var padSize = PAD_SIZE[version];\n  var pad = data.readUIntBE(0, padSize);\n  if (pad + padSize > data.length) {\n    throw new Error('padding exceeds block size');\n  }\n  keylog('padding', data.slice(0, padSize + pad));\n  var padCheck = Buffer.alloc(pad);\n  padCheck.fill(0);\n  if (padCheck.compare(data.slice(padSize, padSize + pad)) !== 0) {\n    throw new Error('invalid padding');\n  }\n  return data.slice(padSize + pad);\n}\n\nfunction unpad(data, last) {\n  var i = data.length - 1;\n  while(i >= 0) {\n    if (data[i]) {\n      if (last) {\n        if (data[i] !== 2) {\n          throw new Error('last record needs to start padding with a 2');\n        }\n      } else {\n        if (data[i] !== 1) {\n          throw new Error('last record needs to start padding with a 2');\n        }\n      }\n      return data.slice(0, i);\n    }\n    --i;\n  }\n  throw new Error('all zero plaintext');\n}\n\nfunction decryptRecord(key, counter, buffer, header, last) {\n  keylog('decrypt', buffer);\n  var nonce = generateNonce(key.nonce, counter);\n  var gcm = crypto.createDecipheriv(AES_GCM, key.key, nonce);\n  gcm.setAuthTag(buffer.slice(buffer.length - TAG_LENGTH));\n  var data = gcm.update(buffer.slice(0, buffer.length - TAG_LENGTH));\n  data = Buffer.concat([data, gcm.final()]);\n  keylog('decrypted', data);\n  if (header.version !== 'aes128gcm') {\n    return unpadLegacy(data, header.version);\n  }\n  return unpad(data, last);\n}\n\n/**\n * Decrypt some bytes.  This uses the parameters to determine the key and block\n * size, which are described in the draft.  Binary values are base64url encoded.\n *\n * |params.version| contains the version of encoding to use: aes128gcm is the latest,\n * but aesgcm is also accepted (though the latter might\n * disappear in a future release).  If omitted, assume aes128gcm.\n *\n * If |params.key| is specified, that value is used as the key.\n *\n * If the version is aes128gcm, the keyid is extracted from the header and used\n * as the ECDH public key of the sender.  For version aesgcm ,\n * |params.dh| needs to be provided with the public key of the sender.\n *\n * The |params.privateKey| includes the private key of the receiver.\n */\nfunction decrypt(buffer, params, keyLookupCallback) {\n  var header = parseParams(params);\n  if (header.version === 'aes128gcm') {\n    var headerLength = readHeader(buffer, header);\n    buffer = buffer.slice(headerLength);\n  }\n  var key = deriveKeyAndNonce(header, MODE_DECRYPT, keyLookupCallback);\n  var start = 0;\n  var result = Buffer.alloc(0);\n\n  var chunkSize = header.rs;\n  if (header.version !== 'aes128gcm') {\n    chunkSize += TAG_LENGTH;\n  }\n\n  for (var i = 0; start < buffer.length; ++i) {\n    var end = start + chunkSize;\n    if (header.version !== 'aes128gcm' && end === buffer.length) {\n      throw new Error('Truncated payload');\n    }\n    end = Math.min(end, buffer.length);\n    if (end - start <= TAG_LENGTH) {\n      throw new Error('Invalid block: too small at ' + i);\n    }\n    var block = decryptRecord(key, i, buffer.slice(start, end),\n                              header, end >= buffer.length);\n    result = Buffer.concat([result, block]);\n    start = end;\n  }\n  return result;\n}\n\nfunction encryptRecord(key, counter, buffer, pad, header, last) {\n  keylog('encrypt', buffer);\n  pad = pad || 0;\n  var nonce = generateNonce(key.nonce, counter);\n  var gcm = crypto.createCipheriv(AES_GCM, key.key, nonce);\n\n  var ciphertext = [];\n  var padSize = PAD_SIZE[header.version];\n  var padding = Buffer.alloc(pad + padSize);\n  padding.fill(0);\n\n  if (header.version !== 'aes128gcm') {\n    padding.writeUIntBE(pad, 0, padSize);\n    keylog('padding', padding);\n    ciphertext.push(gcm.update(padding));\n    ciphertext.push(gcm.update(buffer));\n\n    if (!last && padding.length + buffer.length < header.rs) {\n      throw new Error('Unable to pad to record size');\n    }\n  } else {\n    ciphertext.push(gcm.update(buffer));\n    padding.writeUIntBE(last ? 2 : 1, 0, 1);\n    keylog('padding', padding);\n    ciphertext.push(gcm.update(padding));\n  }\n\n  gcm.final();\n  var tag = gcm.getAuthTag();\n  if (tag.length !== TAG_LENGTH) {\n    throw new Error('invalid tag generated');\n  }\n  ciphertext.push(tag);\n  return keylog('encrypted', Buffer.concat(ciphertext));\n}\n\nfunction writeHeader(header) {\n  var ints = Buffer.alloc(5);\n  var keyid = Buffer.from(header.keyid || []);\n  if (keyid.length > 255) {\n    throw new Error('keyid is too large');\n  }\n  ints.writeUIntBE(header.rs, 0, 4);\n  ints.writeUIntBE(keyid.length, 4, 1);\n  return Buffer.concat([header.salt, ints, keyid]);\n}\n\n/**\n * Encrypt some bytes.  This uses the parameters to determine the key and block\n * size, which are described in the draft.\n *\n * |params.version| contains the version of encoding to use: aes128gcm is the latest,\n * but aesgcm is also accepted (though the latter two might\n * disappear in a future release).  If omitted, assume aes128gcm.\n *\n * If |params.key| is specified, that value is used as the key.\n *\n * For Diffie-Hellman (WebPush), |params.dh| includes the public key of the\n * receiver.  |params.privateKey| is used to establish a shared secret.  Key\n * pairs can be created using |crypto.createECDH()|.\n */\nfunction encrypt(buffer, params, keyLookupCallback) {  \n  if (!Buffer.isBuffer(buffer)) {\n    throw new Error('buffer argument must be a Buffer');\n  }\n  var header = parseParams(params);\n  if (!header.salt) {\n    header.salt = crypto.randomBytes(KEY_LENGTH);\n  }\n\n  var result;\n  if (header.version === 'aes128gcm') {\n    // Save the DH public key in the header unless keyid is set.\n    if (header.privateKey && !header.keyid) {\n      header.keyid = header.privateKey.getPublicKey();\n    }\n    result = writeHeader(header);\n  } else {\n    // No header on other versions\n    result = Buffer.alloc(0);\n  }\n\n  var key = deriveKeyAndNonce(header, MODE_ENCRYPT, keyLookupCallback);\n  var start = 0;\n  var padSize = PAD_SIZE[header.version];\n  var overhead = padSize;\n  if (header.version === 'aes128gcm') {\n    overhead += TAG_LENGTH;\n  }\n  var pad = isNaN(parseInt(params.pad, 10)) ? 0 : parseInt(params.pad, 10);\n\n  var counter = 0;\n  var last = false;\n  while (!last) {\n    // Pad so that at least one data byte is in a block.\n    var recordPad = Math.min(header.rs - overhead - 1, pad);\n    if (header.version !== 'aes128gcm') {\n      recordPad = Math.min((1 << (padSize * 8)) - 1, recordPad);\n    }\n    if (pad > 0 && recordPad === 0) {\n      ++recordPad; // Deal with perverse case of rs=overhead+1 with padding.\n    }\n    pad -= recordPad;\n\n    var end = start + header.rs - overhead - recordPad;\n    if (header.version !== 'aes128gcm') {\n      // The > here ensures that we write out a padding-only block at the end\n      // of a buffer.\n      last = end > buffer.length;\n    } else {\n      last = end >= buffer.length;\n    }\n    last = last && pad <= 0;\n    var block = encryptRecord(key, counter, buffer.slice(start, end),\n                              recordPad, header, last);\n    result = Buffer.concat([result, block]);\n\n    start = end;\n    ++counter;\n  }\n  return result;\n}\n\n\nfunction isFunction(object) {\n  return typeof(object) === 'function';\n }\n\nmodule.exports = {\n  decrypt: decrypt,\n  encrypt: encrypt\n};\n", "'use strict';\n\nconst crypto = require('crypto');\nconst ece = require('http_ece');\n\nconst encrypt = function(userPublicKey, userAuth, payload, contentEncoding) {\n  if (!userPublicKey) {\n    throw new Error('No user public key provided for encryption.');\n  }\n\n  if (typeof userPublicKey !== 'string') {\n    throw new Error('The subscription p256dh value must be a string.');\n  }\n\n  if (Buffer.from(userPublicKey, 'base64url').length !== 65) {\n    throw new Error('The subscription p256dh value should be 65 bytes long.');\n  }\n\n  if (!userAuth) {\n    throw new Error('No user auth provided for encryption.');\n  }\n\n  if (typeof userAuth !== 'string') {\n    throw new Error('The subscription auth key must be a string.');\n  }\n\n  if (Buffer.from(userAuth, 'base64url').length < 16) {\n    throw new Error('The subscription auth key should be at least 16 '\n    + 'bytes long');\n  }\n\n  if (typeof payload !== 'string' && !Buffer.isBuffer(payload)) {\n    throw new Error('Payload must be either a string or a Node Buffer.');\n  }\n\n  if (typeof payload === 'string' || payload instanceof String) {\n    payload = Buffer.from(payload);\n  }\n\n  const localCurve = crypto.createECDH('prime256v1');\n  const localPublicKey = localCurve.generateKeys();\n\n  const salt = crypto.randomBytes(16).toString('base64url');\n\n  const cipherText = ece.encrypt(payload, {\n    version: contentEncoding,\n    dh: userPublicKey,\n    privateKey: localCurve,\n    salt: salt,\n    authSecret: userAuth\n  });\n\n  return {\n    localPublicKey: localPublicKey,\n    salt: salt,\n    cipherText: cipherText\n  };\n};\n\nmodule.exports = {\n  encrypt: encrypt\n};\n", "'use strict';\n\nfunction WebPushError(message, statusCode, headers, body, endpoint) {\n  Error.captureStackTrace(this, this.constructor);\n\n  this.name = this.constructor.name;\n  this.message = message;\n  this.statusCode = statusCode;\n  this.headers = headers;\n  this.body = body;\n  this.endpoint = endpoint;\n}\n\nrequire('util').inherits(WebPushError, Error);\n\nmodule.exports = WebPushError;\n", null, null, null, null, "'use strict';\n\nconst url = require('url');\nconst https = require('https');\n\nconst WebPushError = require('./web-push-error.js');\nconst vapidHelper = require('./vapid-helper.js');\nconst encryptionHelper = require('./encryption-helper.js');\nconst webPushConstants = require('./web-push-constants.js');\nconst urlBase64Helper = require('./urlsafe-base64-helper');\n\n// Default TTL is four weeks.\nconst DEFAULT_TTL = 2419200;\n\nlet gcmAPIKey = '';\nlet vapidDetails;\n\nfunction WebPushLib() {\n\n}\n\n/**\n * When sending messages to a GCM endpoint you need to set the GCM API key\n * by either calling setGMAPIKey() or passing in the API key as an option\n * to sendNotification().\n * @param  {string} apiKey The API key to send with the GCM request.\n */\nWebPushLib.prototype.setGCMAPIKey = function(apiKey) {\n  if (apiKey === null) {\n    gcmAPIKey = null;\n    return;\n  }\n\n  if (typeof apiKey === 'undefined'\n  || typeof apiKey !== 'string'\n  || apiKey.length === 0) {\n    throw new Error('The GCM API Key should be a non-empty string or null.');\n  }\n\n  gcmAPIKey = apiKey;\n};\n\n/**\n * When making requests where you want to define VAPID details, call this\n * method before sendNotification() or pass in the details and options to\n * sendNotification.\n * @param  {string} subject    This must be either a URL or a 'mailto:'\n * address. For example: 'https://my-site.com/contact' or\n * 'mailto: <EMAIL>'\n * @param  {string} publicKey  The public VAPID key, a URL safe, base64 encoded string\n * @param  {string} privateKey The private VAPID key, a URL safe, base64 encoded string.\n */\nWebPushLib.prototype.setVapidDetails = function(subject, publicKey, privateKey) {\n    if (arguments.length === 1 && arguments[0] === null) {\n      vapidDetails = null;\n      return;\n    }\n\n    vapidHelper.validateSubject(subject);\n    vapidHelper.validatePublicKey(publicKey);\n    vapidHelper.validatePrivateKey(privateKey);\n\n    vapidDetails = {\n      subject: subject,\n      publicKey: publicKey,\n      privateKey: privateKey\n    };\n  };\n\n  /**\n   * To get the details of a request to trigger a push message, without sending\n   * a push notification call this method.\n   *\n   * This method will throw an error if there is an issue with the input.\n   * @param  {PushSubscription} subscription The PushSubscription you wish to\n   * send the notification to.\n   * @param  {string|Buffer} [payload]       The payload you wish to send to the\n   * the user.\n   * @param  {Object} [options]              Options for the GCM API key and\n   * vapid keys can be passed in if they are unique for each notification you\n   * wish to send.\n   * @return {Object}                       This method returns an Object which\n   * contains 'endpoint', 'method', 'headers' and 'payload'.\n   */\nWebPushLib.prototype.generateRequestDetails = function(subscription, payload, options) {\n    if (!subscription || !subscription.endpoint) {\n      throw new Error('You must pass in a subscription with at least '\n      + 'an endpoint.');\n    }\n\n    if (typeof subscription.endpoint !== 'string'\n    || subscription.endpoint.length === 0) {\n      throw new Error('The subscription endpoint must be a string with '\n      + 'a valid URL.');\n    }\n\n    if (payload) {\n      // Validate the subscription keys\n      if (typeof subscription !== 'object' || !subscription.keys\n      || !subscription.keys.p256dh\n      || !subscription.keys.auth) {\n        throw new Error('To send a message with a payload, the '\n        + 'subscription must have \\'auth\\' and \\'p256dh\\' keys.');\n      }\n    }\n\n    let currentGCMAPIKey = gcmAPIKey;\n    let currentVapidDetails = vapidDetails;\n    let timeToLive = DEFAULT_TTL;\n    let extraHeaders = {};\n    let contentEncoding = webPushConstants.supportedContentEncodings.AES_128_GCM;\n    let urgency = webPushConstants.supportedUrgency.NORMAL;\n    let topic;\n    let proxy;\n    let agent;\n    let timeout;\n\n    if (options) {\n      const validOptionKeys = [\n        'headers',\n        'gcmAPIKey',\n        'vapidDetails',\n        'TTL',\n        'contentEncoding',\n        'urgency',\n        'topic',\n        'proxy',\n        'agent',\n        'timeout'\n      ];\n      const optionKeys = Object.keys(options);\n      for (let i = 0; i < optionKeys.length; i += 1) {\n        const optionKey = optionKeys[i];\n        if (!validOptionKeys.includes(optionKey)) {\n          throw new Error('\\'' + optionKey + '\\' is an invalid option. '\n          + 'The valid options are [\\'' + validOptionKeys.join('\\', \\'')\n          + '\\'].');\n        }\n      }\n\n      if (options.headers) {\n        extraHeaders = options.headers;\n        let duplicates = Object.keys(extraHeaders)\n            .filter(function (header) {\n              return typeof options[header] !== 'undefined';\n            });\n\n        if (duplicates.length > 0) {\n          throw new Error('Duplicated headers defined ['\n          + duplicates.join(',') + ']. Please either define the header in the'\n          + 'top level options OR in the \\'headers\\' key.');\n        }\n      }\n\n      if (options.gcmAPIKey) {\n        currentGCMAPIKey = options.gcmAPIKey;\n      }\n\n      // Falsy values are allowed here so one can skip Vapid `else if` below and use FCM\n      if (options.vapidDetails !== undefined) {\n        currentVapidDetails = options.vapidDetails;\n      }\n\n      if (options.TTL !== undefined) {\n        timeToLive = Number(options.TTL);\n        if (timeToLive < 0) {\n          throw new Error('TTL should be a number and should be at least 0');\n        }\n      }\n\n      if (options.contentEncoding) {\n        if ((options.contentEncoding === webPushConstants.supportedContentEncodings.AES_128_GCM\n          || options.contentEncoding === webPushConstants.supportedContentEncodings.AES_GCM)) {\n          contentEncoding = options.contentEncoding;\n        } else {\n          throw new Error('Unsupported content encoding specified.');\n        }\n      }\n\n      if (options.urgency) {\n        if ((options.urgency === webPushConstants.supportedUrgency.VERY_LOW\n          || options.urgency === webPushConstants.supportedUrgency.LOW\n          || options.urgency === webPushConstants.supportedUrgency.NORMAL\n          || options.urgency === webPushConstants.supportedUrgency.HIGH)) {\n          urgency = options.urgency;\n        } else {\n          throw new Error('Unsupported urgency specified.');\n        }\n      }\n\n      if (options.topic) {\n        if (!urlBase64Helper.validate(options.topic)) {\n          throw new Error('Unsupported characters set use the URL or filename-safe Base64 characters set');\n        }\n        if (options.topic.length > 32) {\n          throw new Error('use maximum of 32 characters from the URL or filename-safe Base64 characters set');\n        }\n        topic = options.topic;\n      }\n\n      if (options.proxy) {\n        if (typeof options.proxy === 'string'\n          || typeof options.proxy.host === 'string') {\n          proxy = options.proxy;\n        } else {\n          console.warn('Attempt to use proxy option, but invalid type it should be a string or proxy options object.');\n        }\n      }\n\n      if (options.agent) {\n        if (options.agent instanceof https.Agent) {\n          if (proxy) {\n            console.warn('Agent option will be ignored because proxy option is defined.');\n          }\n\n          agent = options.agent;\n        } else {\n          console.warn('Wrong type for the agent option, it should be an instance of https.Agent.');\n        }\n      }\n\n      if (typeof options.timeout === 'number') {\n        timeout = options.timeout;\n      }\n    }\n\n    if (typeof timeToLive === 'undefined') {\n      timeToLive = DEFAULT_TTL;\n    }\n\n    const requestDetails = {\n      method: 'POST',\n      headers: {\n        TTL: timeToLive\n      }\n    };\n    Object.keys(extraHeaders).forEach(function (header) {\n      requestDetails.headers[header] = extraHeaders[header];\n    });\n    let requestPayload = null;\n\n    if (payload) {\n      const encrypted = encryptionHelper\n        .encrypt(subscription.keys.p256dh, subscription.keys.auth, payload, contentEncoding);\n\n      requestDetails.headers['Content-Length'] = encrypted.cipherText.length;\n      requestDetails.headers['Content-Type'] = 'application/octet-stream';\n\n      if (contentEncoding === webPushConstants.supportedContentEncodings.AES_128_GCM) {\n        requestDetails.headers['Content-Encoding'] = webPushConstants.supportedContentEncodings.AES_128_GCM;\n      } else if (contentEncoding === webPushConstants.supportedContentEncodings.AES_GCM) {\n        requestDetails.headers['Content-Encoding'] = webPushConstants.supportedContentEncodings.AES_GCM;\n        requestDetails.headers.Encryption = 'salt=' + encrypted.salt;\n        requestDetails.headers['Crypto-Key'] = 'dh=' + encrypted.localPublicKey.toString('base64url');\n      }\n\n      requestPayload = encrypted.cipherText;\n    } else {\n      requestDetails.headers['Content-Length'] = 0;\n    }\n\n    const isGCM = subscription.endpoint.startsWith('https://android.googleapis.com/gcm/send');\n    const isFCM = subscription.endpoint.startsWith('https://fcm.googleapis.com/fcm/send');\n    // VAPID isn't supported by GCM hence the if, else if.\n    if (isGCM) {\n      if (!currentGCMAPIKey) {\n        console.warn('Attempt to send push notification to GCM endpoint, '\n        + 'but no GCM key is defined. Please use setGCMApiKey() or add '\n        + '\\'gcmAPIKey\\' as an option.');\n      } else {\n        requestDetails.headers.Authorization = 'key=' + currentGCMAPIKey;\n      }\n    } else if (currentVapidDetails) {\n      const parsedUrl = url.parse(subscription.endpoint);\n      const audience = parsedUrl.protocol + '//'\n      + parsedUrl.host;\n\n      const vapidHeaders = vapidHelper.getVapidHeaders(\n        audience,\n        currentVapidDetails.subject,\n        currentVapidDetails.publicKey,\n        currentVapidDetails.privateKey,\n        contentEncoding\n      );\n\n      requestDetails.headers.Authorization = vapidHeaders.Authorization;\n\n      if (contentEncoding === webPushConstants.supportedContentEncodings.AES_GCM) {\n        if (requestDetails.headers['Crypto-Key']) {\n          requestDetails.headers['Crypto-Key'] += ';'\n          + vapidHeaders['Crypto-Key'];\n        } else {\n          requestDetails.headers['Crypto-Key'] = vapidHeaders['Crypto-Key'];\n        }\n      }\n    } else if (isFCM && currentGCMAPIKey) {\n      requestDetails.headers.Authorization = 'key=' + currentGCMAPIKey;\n    }\n\n    requestDetails.headers.Urgency = urgency;\n\n    if (topic) {\n      requestDetails.headers.Topic = topic;\n    }\n\n    requestDetails.body = requestPayload;\n    requestDetails.endpoint = subscription.endpoint;\n\n    if (proxy) {\n      requestDetails.proxy = proxy;\n    }\n\n    if (agent) {\n      requestDetails.agent = agent;\n    }\n\n    if (timeout) {\n      requestDetails.timeout = timeout;\n    }\n\n    return requestDetails;\n  };\n\n/**\n * To send a push notification call this method with a subscription, optional\n * payload and any options.\n * @param  {PushSubscription} subscription The PushSubscription you wish to\n * send the notification to.\n * @param  {string|Buffer} [payload]       The payload you wish to send to the\n * the user.\n * @param  {Object} [options]              Options for the GCM API key and\n * vapid keys can be passed in if they are unique for each notification you\n * wish to send.\n * @return {Promise}                       This method returns a Promise which\n * resolves if the sending of the notification was successful, otherwise it\n * rejects.\n */\nWebPushLib.prototype.sendNotification = function(subscription, payload, options) {\n    let requestDetails;\n    try {\n      requestDetails = this.generateRequestDetails(subscription, payload, options);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n\n    return new Promise(function(resolve, reject) {\n      const httpsOptions = {};\n      const urlParts = url.parse(requestDetails.endpoint);\n      httpsOptions.hostname = urlParts.hostname;\n      httpsOptions.port = urlParts.port;\n      httpsOptions.path = urlParts.path;\n\n      httpsOptions.headers = requestDetails.headers;\n      httpsOptions.method = requestDetails.method;\n\n      if (requestDetails.timeout) {\n        httpsOptions.timeout = requestDetails.timeout;\n      }\n\n      if (requestDetails.agent) {\n        httpsOptions.agent = requestDetails.agent;\n      }\n\n      if (requestDetails.proxy) {\n        const { HttpsProxyAgent } = require('https-proxy-agent'); // eslint-disable-line global-require\n        httpsOptions.agent = new HttpsProxyAgent(requestDetails.proxy);\n      }\n\n      const pushRequest = https.request(httpsOptions, function(pushResponse) {\n        let responseText = '';\n\n        pushResponse.on('data', function(chunk) {\n          responseText += chunk;\n        });\n\n        pushResponse.on('end', function() {\n          if (pushResponse.statusCode < 200 || pushResponse.statusCode > 299) {\n            reject(new WebPushError(\n              'Received unexpected response code',\n              pushResponse.statusCode,\n              pushResponse.headers,\n              responseText,\n              requestDetails.endpoint\n            ));\n          } else {\n            resolve({\n              statusCode: pushResponse.statusCode,\n              body: responseText,\n              headers: pushResponse.headers\n            });\n          }\n        });\n      });\n\n      if (requestDetails.timeout) {\n        pushRequest.on('timeout', function() {\n          pushRequest.destroy(new Error('Socket timeout'));\n        });\n      }\n\n      pushRequest.on('error', function(e) {\n        reject(e);\n      });\n\n      if (requestDetails.body) {\n        pushRequest.write(requestDetails.body);\n      }\n\n      pushRequest.end();\n    });\n  };\n\nmodule.exports = WebPushLib;\n", "'use strict';\n\nconst vapidHelper = require('./vapid-helper.js');\nconst encryptionHelper = require('./encryption-helper.js');\nconst WebPushLib = require('./web-push-lib.js');\nconst WebPushError = require('./web-push-error.js');\nconst WebPushConstants = require('./web-push-constants.js');\n\nconst webPush = new WebPushLib();\n\nmodule.exports = {\n  WebPushError: WebPushError,\n  supportedContentEncodings: WebPushConstants.supportedContentEncodings,\n  encrypt: encryptionHelper.encrypt,\n  getVapidHeaders: vapidHelper.getVapidHeaders,\n  generateVAPIDKeys: vapidHelper.generateVAPIDKeys,\n  setGCMAPIKey: webPush.setGCMAPIKey,\n  setVapidDetails: webPush.setVapidDetails,\n  generateRequestDetails: webPush.generateRequestDetails,\n  sendNotification: webPush.sendNotification.bind(webPush)\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAIA,UAAS,OAAO;AAGpB,aAAS,UAAW,KAAK,KAAK;AAC5B,eAAS,OAAO,KAAK;AACnB,YAAI,GAAG,IAAI,IAAI,GAAG;AAAA,MACpB;AAAA,IACF;AACA,QAAIA,QAAO,QAAQA,QAAO,SAASA,QAAO,eAAeA,QAAO,iBAAiB;AAC/E,aAAO,UAAU;AAAA,IACnB,OAAO;AAEL,gBAAU,QAAQ,OAAO;AACzB,cAAQ,SAAS;AAAA,IACnB;AAEA,aAAS,WAAY,KAAK,kBAAkB,QAAQ;AAClD,aAAOA,QAAO,KAAK,kBAAkB,MAAM;AAAA,IAC7C;AAEA,eAAW,YAAY,OAAO,OAAOA,QAAO,SAAS;AAGrD,cAAUA,SAAQ,UAAU;AAE5B,eAAW,OAAO,SAAU,KAAK,kBAAkB,QAAQ;AACzD,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AACA,aAAOA,QAAO,KAAK,kBAAkB,MAAM;AAAA,IAC7C;AAEA,eAAW,QAAQ,SAAU,MAAM,MAAM,UAAU;AACjD,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACjD;AACA,UAAI,MAAMA,QAAO,IAAI;AACrB,UAAI,SAAS,QAAW;AACtB,YAAI,OAAO,aAAa,UAAU;AAChC,cAAI,KAAK,MAAM,QAAQ;AAAA,QACzB,OAAO;AACL,cAAI,KAAK,IAAI;AAAA,QACf;AAAA,MACF,OAAO;AACL,YAAI,KAAK,CAAC;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AAEA,eAAW,cAAc,SAAU,MAAM;AACvC,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACjD;AACA,aAAOA,QAAO,IAAI;AAAA,IACpB;AAEA,eAAW,kBAAkB,SAAU,MAAM;AAC3C,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACjD;AACA,aAAO,OAAO,WAAW,IAAI;AAAA,IAC/B;AAAA;AAAA;;;AChEA;AAAA;AACA,QAAIC,UAAS,sBAAuB;AACpC,QAAI,SAAS;AACb,QAAI,OAAO;AAEX,aAAS,WAAW,MAAM;AACxB,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,WAAW;AAGhB,UAAI,CAAC,MAAM;AACT,aAAK,SAASA,QAAO,MAAM,CAAC;AAC5B,eAAO;AAAA,MACT;AAGA,UAAI,OAAO,KAAK,SAAS,YAAY;AACnC,aAAK,SAASA,QAAO,MAAM,CAAC;AAC5B,aAAK,KAAK,IAAI;AACd,eAAO;AAAA,MACT;AAIA,UAAI,KAAK,UAAU,OAAO,SAAS,UAAU;AAC3C,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,gBAAQ,UAAS,WAAY;AAC3B,eAAK,KAAK,OAAO,IAAI;AACrB,eAAK,WAAW;AAChB,eAAK,KAAK,OAAO;AAAA,QACnB,GAAE,KAAK,IAAI,CAAC;AACZ,eAAO;AAAA,MACT;AAEA,YAAM,IAAI,UAAU,2BAA0B,OAAO,OAAO,GAAG;AAAA,IACjE;AACA,SAAK,SAAS,YAAY,MAAM;AAEhC,eAAW,UAAU,QAAQ,SAAS,MAAM,MAAM;AAChD,WAAK,SAASA,QAAO,OAAO,CAAC,KAAK,QAAQA,QAAO,KAAK,IAAI,CAAC,CAAC;AAC5D,WAAK,KAAK,QAAQ,IAAI;AAAA,IACxB;AAEA,eAAW,UAAU,MAAM,SAAS,IAAI,MAAM;AAC5C,UAAI;AACF,aAAK,MAAM,IAAI;AACjB,WAAK,KAAK,OAAO,IAAI;AACrB,WAAK,KAAK,OAAO;AACjB,WAAK,WAAW;AAChB,WAAK,WAAW;AAAA,IAClB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtDjB;AAAA;AAAA;AAEA,aAAS,aAAa,SAAS;AAC9B,UAAI,UAAW,UAAU,IAAK,MAAM,UAAU,MAAM,IAAI,IAAI;AAC5D,aAAO;AAAA,IACR;AAEA,QAAI,mBAAmB;AAAA,MACtB,OAAO,aAAa,GAAG;AAAA,MACvB,OAAO,aAAa,GAAG;AAAA,MACvB,OAAO,aAAa,GAAG;AAAA,IACxB;AAEA,aAAS,oBAAoB,KAAK;AACjC,UAAI,aAAa,iBAAiB,GAAG;AACrC,UAAI,YAAY;AACf,eAAO;AAAA,MACR;AAEA,YAAM,IAAI,MAAM,wBAAwB,MAAM,GAAG;AAAA,IAClD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA;AAEA,QAAIC,UAAS,sBAAuB;AAEpC,QAAI,sBAAsB;AAE1B,QAAI,YAAY;AAAhB,QACC,kBAAkB;AADnB,QAEC,gBAAgB;AAFjB,QAGC,UAAU;AAHX,QAIC,UAAU;AAJX,QAKC,kBAAmB,UAAU,gBAAkB,mBAAmB;AALnE,QAMC,kBAAkB,UAAW,mBAAmB;AAEjD,aAAS,UAAU,QAAQ;AAC1B,aAAO,OACL,QAAQ,MAAM,EAAE,EAChB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG;AAAA,IACrB;AAEA,aAAS,kBAAkB,WAAW;AACrC,UAAIA,QAAO,SAAS,SAAS,GAAG;AAC/B,eAAO;AAAA,MACR,WAAW,aAAa,OAAO,WAAW;AACzC,eAAOA,QAAO,KAAK,WAAW,QAAQ;AAAA,MACvC;AAEA,YAAM,IAAI,UAAU,qDAAqD;AAAA,IAC1E;AAEA,aAAS,UAAU,WAAW,KAAK;AAClC,kBAAY,kBAAkB,SAAS;AACvC,UAAI,aAAa,oBAAoB,GAAG;AAIxC,UAAI,wBAAwB,aAAa;AAEzC,UAAI,cAAc,UAAU;AAE5B,UAAI,SAAS;AACb,UAAI,UAAU,QAAQ,MAAM,iBAAiB;AAC5C,cAAM,IAAI,MAAM,+BAA+B;AAAA,MAChD;AAEA,UAAI,YAAY,UAAU,QAAQ;AAClC,UAAI,eAAe,YAAY,IAAI;AAClC,oBAAY,UAAU,QAAQ;AAAA,MAC/B;AAEA,UAAI,cAAc,SAAS,WAAW;AACrC,cAAM,IAAI,MAAM,gCAAgC,YAAY,eAAe,cAAc,UAAU,aAAa;AAAA,MACjH;AAEA,UAAI,UAAU,QAAQ,MAAM,iBAAiB;AAC5C,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACxD;AAEA,UAAI,UAAU,UAAU,QAAQ;AAEhC,UAAI,cAAc,SAAS,IAAI,SAAS;AACvC,cAAM,IAAI,MAAM,8BAA8B,UAAU,eAAe,cAAc,SAAS,KAAK,aAAa;AAAA,MACjH;AAEA,UAAI,wBAAwB,SAAS;AACpC,cAAM,IAAI,MAAM,8BAA8B,UAAU,gBAAgB,wBAAwB,iBAAiB;AAAA,MAClH;AAEA,UAAI,UAAU;AACd,gBAAU;AAEV,UAAI,UAAU,QAAQ,MAAM,iBAAiB;AAC5C,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACxD;AAEA,UAAI,UAAU,UAAU,QAAQ;AAEhC,UAAI,cAAc,WAAW,SAAS;AACrC,cAAM,IAAI,MAAM,8BAA8B,UAAU,mBAAmB,cAAc,UAAU,GAAG;AAAA,MACvG;AAEA,UAAI,wBAAwB,SAAS;AACpC,cAAM,IAAI,MAAM,8BAA8B,UAAU,gBAAgB,wBAAwB,iBAAiB;AAAA,MAClH;AAEA,UAAI,UAAU;AACd,gBAAU;AAEV,UAAI,WAAW,aAAa;AAC3B,cAAM,IAAI,MAAM,8CAA8C,cAAc,UAAU,gBAAgB;AAAA,MACvG;AAEA,UAAI,WAAW,aAAa,SAC3B,WAAW,aAAa;AAEzB,UAAI,MAAMA,QAAO,YAAY,WAAW,UAAU,WAAW,OAAO;AAEpE,WAAK,SAAS,GAAG,SAAS,UAAU,EAAE,QAAQ;AAC7C,YAAI,MAAM,IAAI;AAAA,MACf;AACA,gBAAU,KAAK,KAAK,QAAQ,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,OAAO;AAE/E,eAAS;AAET,eAAS,IAAI,QAAQ,SAAS,IAAI,UAAU,EAAE,QAAQ;AACrD,YAAI,MAAM,IAAI;AAAA,MACf;AACA,gBAAU,KAAK,KAAK,QAAQ,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,OAAO;AAE/E,YAAM,IAAI,SAAS,QAAQ;AAC3B,YAAM,UAAU,GAAG;AAEnB,aAAO;AAAA,IACR;AAEA,aAAS,aAAa,KAAK,OAAO,MAAM;AACvC,UAAI,UAAU;AACd,aAAO,QAAQ,UAAU,QAAQ,IAAI,QAAQ,OAAO,MAAM,GAAG;AAC5D,UAAE;AAAA,MACH;AAEA,UAAI,YAAY,IAAI,QAAQ,OAAO,KAAK;AACxC,UAAI,WAAW;AACd,UAAE;AAAA,MACH;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,UAAU,WAAW,KAAK;AAClC,kBAAY,kBAAkB,SAAS;AACvC,UAAI,aAAa,oBAAoB,GAAG;AAExC,UAAI,iBAAiB,UAAU;AAC/B,UAAI,mBAAmB,aAAa,GAAG;AACtC,cAAM,IAAI,UAAU,MAAM,MAAM,2BAA2B,aAAa,IAAI,mBAAmB,iBAAiB,GAAG;AAAA,MACpH;AAEA,UAAI,WAAW,aAAa,WAAW,GAAG,UAAU;AACpD,UAAI,WAAW,aAAa,WAAW,YAAY,UAAU,MAAM;AACnE,UAAI,UAAU,aAAa;AAC3B,UAAI,UAAU,aAAa;AAE3B,UAAI,UAAU,IAAI,IAAI,UAAU,IAAI,IAAI;AAExC,UAAI,cAAc,UAAU;AAE5B,UAAI,MAAMA,QAAO,aAAa,cAAc,IAAI,KAAK,OAAO;AAE5D,UAAI,SAAS;AACb,UAAI,QAAQ,IAAI;AAChB,UAAI,aAAa;AAGhB,YAAI,QAAQ,IAAI;AAAA,MACjB,OAAO;AAGN,YAAI,QAAQ,IAAI,YAAY;AAE5B,YAAI,QAAQ,IAAI,UAAU;AAAA,MAC3B;AACA,UAAI,QAAQ,IAAI;AAChB,UAAI,QAAQ,IAAI;AAChB,UAAI,WAAW,GAAG;AACjB,YAAI,QAAQ,IAAI;AAChB,kBAAU,UAAU,KAAK,KAAK,QAAQ,GAAG,UAAU;AAAA,MACpD,OAAO;AACN,kBAAU,UAAU,KAAK,KAAK,QAAQ,UAAU,UAAU;AAAA,MAC3D;AACA,UAAI,QAAQ,IAAI;AAChB,UAAI,QAAQ,IAAI;AAChB,UAAI,WAAW,GAAG;AACjB,YAAI,QAAQ,IAAI;AAChB,kBAAU,KAAK,KAAK,QAAQ,UAAU;AAAA,MACvC,OAAO;AACN,kBAAU,KAAK,KAAK,QAAQ,aAAa,QAAQ;AAAA,MAClD;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA,MAChB;AAAA,MACA;AAAA,IACD;AAAA;AAAA;;;AC1LA;AAAA;AAAA;AAEA,QAAIC,UAAS,iBAAkB;AAC/B,QAAI,aAAa,iBAAkB;AAEnC,WAAO,UAAU;AAEjB,aAAS,SAAS,GAAG,GAAG;AAGtB,UAAI,CAACA,QAAO,SAAS,CAAC,KAAK,CAACA,QAAO,SAAS,CAAC,GAAG;AAC9C,eAAO;AAAA,MACT;AAKA,UAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAEjC,aAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACjB;AACA,aAAO,MAAM;AAAA,IACf;AAEA,aAAS,UAAU,WAAW;AAC5B,MAAAA,QAAO,UAAU,QAAQ,WAAW,UAAU,QAAQ,SAAS,MAAM,MAAM;AACzE,eAAO,SAAS,MAAM,IAAI;AAAA,MAC5B;AAAA,IACF;AAEA,QAAI,eAAeA,QAAO,UAAU;AACpC,QAAI,mBAAmB,WAAW,UAAU;AAC5C,aAAS,UAAU,WAAW;AAC5B,MAAAA,QAAO,UAAU,QAAQ;AACzB,iBAAW,UAAU,QAAQ;AAAA,IAC/B;AAAA;AAAA;;;ACxCA;AAAA;AAAA,QAAIC,UAAS,sBAAuB;AACpC,QAAI,SAAS;AACb,QAAI,cAAc;AAClB,QAAI,OAAO;AAEX,QAAI,wBAAwB;AAC5B,QAAI,qBAAqB;AACzB,QAAI,2BAA2B;AAC/B,QAAI,yBAAyB;AAE7B,QAAI,qBAAqB,OAAO,OAAO,oBAAoB;AAC3D,QAAI,oBAAoB;AACtB,kCAA4B;AAC5B,4BAAsB;AAAA,IACxB;AAEA,aAAS,iBAAiB,KAAK;AAC7B,UAAIA,QAAO,SAAS,GAAG,GAAG;AACxB;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B;AAAA,MACF;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,UAAU,wBAAwB;AAAA,MAC1C;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,UAAU,wBAAwB;AAAA,MAC1C;AAEA,UAAI,OAAO,IAAI,SAAS,UAAU;AAChC,cAAM,UAAU,wBAAwB;AAAA,MAC1C;AAEA,UAAI,OAAO,IAAI,sBAAsB,UAAU;AAC7C,cAAM,UAAU,wBAAwB;AAAA,MAC1C;AAEA,UAAI,OAAO,IAAI,WAAW,YAAY;AACpC,cAAM,UAAU,wBAAwB;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,kBAAkB,KAAK;AAC9B,UAAIA,QAAO,SAAS,GAAG,GAAG;AACxB;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B;AAAA,MACF;AAEA,YAAM,UAAU,sBAAsB;AAAA,IACxC;AAEA,aAAS,iBAAiB,KAAK;AAC7B,UAAIA,QAAO,SAAS,GAAG,GAAG;AACxB;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,oBAAoB;AACvB,cAAM,UAAU,kBAAkB;AAAA,MACpC;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,UAAU,kBAAkB;AAAA,MACpC;AAEA,UAAI,IAAI,SAAS,UAAU;AACzB,cAAM,UAAU,kBAAkB;AAAA,MACpC;AAEA,UAAI,OAAO,IAAI,WAAW,YAAY;AACpC,cAAM,UAAU,kBAAkB;AAAA,MACpC;AAAA,IACF;AAEA,aAAS,WAAW,QAAQ;AAC1B,aAAO,OACJ,QAAQ,MAAM,EAAE,EAChB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG;AAAA,IACvB;AAEA,aAAS,SAAS,WAAW;AAC3B,kBAAY,UAAU,SAAS;AAE/B,UAAI,UAAU,IAAI,UAAU,SAAS;AACrC,UAAI,YAAY,GAAG;AACjB,iBAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,uBAAa;AAAA,QACf;AAAA,MACF;AAEA,aAAO,UACJ,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,GAAG;AAAA,IACtB;AAEA,aAAS,UAAU,UAAU;AAC3B,UAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACrC,UAAI,SAAS,KAAK,OAAO,KAAK,MAAM,QAAQ,EAAE,MAAM,MAAM,IAAI;AAC9D,aAAO,IAAI,UAAU,MAAM;AAAA,IAC7B;AAEA,aAAS,eAAe,KAAK;AAC3B,aAAOA,QAAO,SAAS,GAAG,KAAK,OAAO,QAAQ;AAAA,IAChD;AAEA,aAAS,eAAe,OAAO;AAC7B,UAAI,CAAC,eAAe,KAAK;AACvB,gBAAQ,KAAK,UAAU,KAAK;AAC9B,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,aAAO,SAAS,KAAK,OAAO,QAAQ;AAClC,yBAAiB,MAAM;AACvB,gBAAQ,eAAe,KAAK;AAC5B,YAAI,OAAO,OAAO,WAAW,QAAQ,MAAM,MAAM;AACjD,YAAI,OAAO,KAAK,OAAO,KAAK,GAAG,KAAK,OAAO,QAAQ;AACnD,eAAO,WAAW,GAAG;AAAA,MACvB;AAAA,IACF;AAEA,QAAI;AACJ,QAAI,kBAAkB,qBAAqB,SAAS,SAASC,iBAAgB,GAAG,GAAG;AACjF,UAAI,EAAE,eAAe,EAAE,YAAY;AACjC,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,gBAAgB,GAAG,CAAC;AAAA,IACpC,IAAI,SAASA,iBAAgB,GAAG,GAAG;AACjC,UAAI,CAAC,aAAa;AAChB,sBAAc;AAAA,MAChB;AAEA,aAAO,YAAY,GAAG,CAAC;AAAA,IACzB;AAEA,aAAS,mBAAmB,MAAM;AAChC,aAAO,SAAS,OAAO,OAAO,WAAW,QAAQ;AAC/C,YAAI,cAAc,iBAAiB,IAAI,EAAE,OAAO,MAAM;AACtD,eAAO,gBAAgBD,QAAO,KAAK,SAAS,GAAGA,QAAO,KAAK,WAAW,CAAC;AAAA,MACzE;AAAA,IACF;AAEA,aAAS,gBAAgB,MAAM;AAC9B,aAAO,SAAS,KAAK,OAAO,YAAY;AACrC,0BAAkB,UAAU;AAC5B,gBAAQ,eAAe,KAAK;AAG5B,YAAI,SAAS,OAAO,WAAW,YAAY,IAAI;AAC/C,YAAI,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK,YAAY,QAAQ;AACjE,eAAO,WAAW,GAAG;AAAA,MACvB;AAAA,IACF;AAEA,aAAS,kBAAkB,MAAM;AAC/B,aAAO,SAAS,OAAO,OAAO,WAAW,WAAW;AAClD,yBAAiB,SAAS;AAC1B,gBAAQ,eAAe,KAAK;AAC5B,oBAAY,SAAS,SAAS;AAC9B,YAAI,WAAW,OAAO,aAAa,YAAY,IAAI;AACnD,iBAAS,OAAO,KAAK;AACrB,eAAO,SAAS,OAAO,WAAW,WAAW,QAAQ;AAAA,MACvD;AAAA,IACF;AAEA,aAAS,mBAAmB,MAAM;AAChC,aAAO,SAAS,KAAK,OAAO,YAAY;AACtC,0BAAkB,UAAU;AAC5B,gBAAQ,eAAe,KAAK;AAC5B,YAAI,SAAS,OAAO,WAAW,YAAY,IAAI;AAC/C,YAAI,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK;AAAA,UAC3C,KAAK;AAAA,UACL,SAAS,OAAO,UAAU;AAAA,UAC1B,YAAY,OAAO,UAAU;AAAA,QAC/B,GAAG,QAAQ;AACX,eAAO,WAAW,GAAG;AAAA,MACvB;AAAA,IACF;AAEA,aAAS,qBAAqB,MAAM;AAClC,aAAO,SAAS,OAAO,OAAO,WAAW,WAAW;AAClD,yBAAiB,SAAS;AAC1B,gBAAQ,eAAe,KAAK;AAC5B,oBAAY,SAAS,SAAS;AAC9B,YAAI,WAAW,OAAO,aAAa,YAAY,IAAI;AACnD,iBAAS,OAAO,KAAK;AACrB,eAAO,SAAS,OAAO;AAAA,UACrB,KAAK;AAAA,UACL,SAAS,OAAO,UAAU;AAAA,UAC1B,YAAY,OAAO,UAAU;AAAA,QAC/B,GAAG,WAAW,QAAQ;AAAA,MACxB;AAAA,IACF;AAEA,aAAS,kBAAkB,MAAM;AAC/B,UAAI,QAAQ,gBAAgB,IAAI;AAChC,aAAO,SAAS,OAAO;AACrB,YAAI,YAAY,MAAM,MAAM,MAAM,SAAS;AAC3C,oBAAY,YAAY,UAAU,WAAW,OAAO,IAAI;AACxD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,mBAAmB,MAAM;AAChC,UAAI,QAAQ,kBAAkB,IAAI;AAClC,aAAO,SAAS,OAAO,OAAO,WAAW,WAAW;AAClD,oBAAY,YAAY,UAAU,WAAW,OAAO,IAAI,EAAE,SAAS,QAAQ;AAC3E,YAAI,SAAS,MAAM,OAAO,WAAW,SAAS;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,mBAAmB;AAC1B,aAAO,SAAS,OAAO;AACrB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,qBAAqB;AAC5B,aAAO,SAAS,OAAO,OAAO,WAAW;AACvC,eAAO,cAAc;AAAA,MACvB;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,IAAI,WAAW;AACvC,UAAI,kBAAkB;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AAAA,QACtB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AACA,UAAI,QAAQ,UAAU,MAAM,uCAAuC;AACnE,UAAI,CAAC;AACH,cAAM,UAAU,uBAAuB,SAAS;AAClD,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG,YAAY;AAC9C,UAAI,OAAO,MAAM,CAAC;AAElB,aAAO;AAAA,QACL,MAAM,gBAAgB,IAAI,EAAE,IAAI;AAAA,QAChC,QAAQ,kBAAkB,IAAI,EAAE,IAAI;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;;;ACzQA;AAAA;AACA,QAAIE,UAAS,iBAAkB;AAE/B,WAAO,UAAU,SAAS,SAAS,KAAK;AACtC,UAAI,OAAO,QAAQ;AACjB,eAAO;AACT,UAAI,OAAO,QAAQ,YAAYA,QAAO,SAAS,GAAG;AAChD,eAAO,IAAI,SAAS;AACtB,aAAO,KAAK,UAAU,GAAG;AAAA,IAC3B;AAAA;AAAA;;;ACTA;AAAA;AACA,QAAIC,UAAS,sBAAuB;AACpC,QAAI,aAAa;AACjB,QAAI,MAAM;AACV,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,aAAS,UAAU,QAAQ,UAAU;AACnC,aAAOA,QACJ,KAAK,QAAQ,QAAQ,EACrB,SAAS,QAAQ,EACjB,QAAQ,MAAM,EAAE,EAChB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG;AAAA,IACvB;AAEA,aAAS,gBAAgB,QAAQ,SAAS,UAAU;AAClD,iBAAW,YAAY;AACvB,UAAI,gBAAgB,UAAU,SAAS,MAAM,GAAG,QAAQ;AACxD,UAAI,iBAAiB,UAAU,SAAS,OAAO,GAAG,QAAQ;AAC1D,aAAO,KAAK,OAAO,SAAS,eAAe,cAAc;AAAA,IAC3D;AAEA,aAAS,QAAQ,MAAM;AACrB,UAAI,SAAS,KAAK;AAClB,UAAI,UAAU,KAAK;AACnB,UAAI,cAAc,KAAK,UAAU,KAAK;AACtC,UAAI,WAAW,KAAK;AACpB,UAAI,OAAO,IAAI,OAAO,GAAG;AACzB,UAAI,eAAe,gBAAgB,QAAQ,SAAS,QAAQ;AAC5D,UAAI,YAAY,KAAK,KAAK,cAAc,WAAW;AACnD,aAAO,KAAK,OAAO,SAAS,cAAc,SAAS;AAAA,IACrD;AAEA,aAAS,WAAW,MAAM;AACxB,UAAI,SAAS,KAAK,UAAQ,KAAK,cAAY,KAAK;AAChD,UAAI,eAAe,IAAI,WAAW,MAAM;AACxC,WAAK,WAAW;AAChB,WAAK,SAAS,KAAK;AACnB,WAAK,WAAW,KAAK;AACrB,WAAK,SAAS,KAAK,aAAa,KAAK,MAAM;AAC3C,WAAK,UAAU,IAAI,WAAW,KAAK,OAAO;AAC1C,WAAK,OAAO,KAAK,UAAS,WAAY;AACpC,YAAI,CAAC,KAAK,QAAQ,YAAY,KAAK;AACjC,eAAK,KAAK;AAAA,MACd,GAAE,KAAK,IAAI,CAAC;AAEZ,WAAK,QAAQ,KAAK,UAAS,WAAY;AACrC,YAAI,CAAC,KAAK,OAAO,YAAY,KAAK;AAChC,eAAK,KAAK;AAAA,MACd,GAAE,KAAK,IAAI,CAAC;AAAA,IACd;AACA,SAAK,SAAS,YAAY,MAAM;AAEhC,eAAW,UAAU,OAAO,SAAS,OAAO;AAC1C,UAAI;AACF,YAAI,YAAY,QAAQ;AAAA,UACtB,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK,QAAQ;AAAA,UACtB,QAAQ,KAAK,OAAO;AAAA,UACpB,UAAU,KAAK;AAAA,QACjB,CAAC;AACD,aAAK,KAAK,QAAQ,SAAS;AAC3B,aAAK,KAAK,QAAQ,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,aAAK,WAAW;AAChB,eAAO;AAAA,MACT,SAAS,GAAG;AACV,aAAK,WAAW;AAChB,aAAK,KAAK,SAAS,CAAC;AACpB,aAAK,KAAK,OAAO;AAAA,MACnB;AAAA,IACF;AAEA,eAAW,OAAO;AAElB,WAAO,UAAU;AAAA;AAAA;;;AC7EjB;AAAA;AACA,QAAIC,UAAS,sBAAuB;AACpC,QAAI,aAAa;AACjB,QAAI,MAAM;AACV,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,OAAO;AACX,QAAI,YAAY;AAEhB,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAAA,IACnD;AAEA,aAAS,cAAc,OAAO;AAC5B,UAAI,SAAS,KAAK;AAChB,eAAO;AACT,UAAI;AAAE,eAAO,KAAK,MAAM,KAAK;AAAA,MAAG,SACzB,GAAG;AAAE,eAAO;AAAA,MAAW;AAAA,IAChC;AAEA,aAAS,cAAc,QAAQ;AAC7B,UAAI,gBAAgB,OAAO,MAAM,KAAK,CAAC,EAAE,CAAC;AAC1C,aAAO,cAAcA,QAAO,KAAK,eAAe,QAAQ,EAAE,SAAS,QAAQ,CAAC;AAAA,IAC9E;AAEA,aAAS,oBAAoB,QAAQ;AACnC,aAAO,OAAO,MAAM,KAAK,CAAC,EAAE,KAAK,GAAG;AAAA,IACtC;AAEA,aAAS,iBAAiB,QAAQ;AAChC,aAAO,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,IAC5B;AAEA,aAAS,eAAe,QAAQ,UAAU;AACxC,iBAAW,YAAY;AACvB,UAAI,UAAU,OAAO,MAAM,GAAG,EAAE,CAAC;AACjC,aAAOA,QAAO,KAAK,SAAS,QAAQ,EAAE,SAAS,QAAQ;AAAA,IACzD;AAEA,aAAS,WAAW,QAAQ;AAC1B,aAAO,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,cAAc,MAAM;AAAA,IACzD;AAEA,aAAS,UAAU,QAAQ,WAAW,aAAa;AACjD,UAAI,CAAC,WAAW;AACd,YAAI,MAAM,IAAI,MAAM,4CAA4C;AAChE,YAAI,OAAO;AACX,cAAM;AAAA,MACR;AACA,eAAS,SAAS,MAAM;AACxB,UAAI,YAAY,iBAAiB,MAAM;AACvC,UAAI,eAAe,oBAAoB,MAAM;AAC7C,UAAI,OAAO,IAAI,SAAS;AACxB,aAAO,KAAK,OAAO,cAAc,WAAW,WAAW;AAAA,IACzD;AAEA,aAAS,UAAU,QAAQ,MAAM;AAC/B,aAAO,QAAQ,CAAC;AAChB,eAAS,SAAS,MAAM;AAExB,UAAI,CAAC,WAAW,MAAM;AACpB,eAAO;AAET,UAAI,SAAS,cAAc,MAAM;AAEjC,UAAI,CAAC;AACH,eAAO;AAET,UAAI,UAAU,eAAe,MAAM;AACnC,UAAI,OAAO,QAAQ,SAAS,KAAK;AAC/B,kBAAU,KAAK,MAAM,SAAS,KAAK,QAAQ;AAE7C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,WAAW,iBAAiB,MAAM;AAAA,MACpC;AAAA,IACF;AAEA,aAAS,aAAa,MAAM;AAC1B,aAAO,QAAQ,CAAC;AAChB,UAAI,cAAc,KAAK,UAAQ,KAAK,aAAW,KAAK;AACpD,UAAI,eAAe,IAAI,WAAW,WAAW;AAC7C,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK;AACtB,WAAK,WAAW,KAAK;AACrB,WAAK,SAAS,KAAK,YAAY,KAAK,MAAM;AAC1C,WAAK,YAAY,IAAI,WAAW,KAAK,SAAS;AAC9C,WAAK,OAAO,KAAK,UAAS,WAAY;AACpC,YAAI,CAAC,KAAK,UAAU,YAAY,KAAK;AACnC,eAAK,OAAO;AAAA,MAChB,GAAE,KAAK,IAAI,CAAC;AAEZ,WAAK,UAAU,KAAK,UAAS,WAAY;AACvC,YAAI,CAAC,KAAK,OAAO,YAAY,KAAK;AAChC,eAAK,OAAO;AAAA,MAChB,GAAE,KAAK,IAAI,CAAC;AAAA,IACd;AACA,SAAK,SAAS,cAAc,MAAM;AAClC,iBAAa,UAAU,SAAS,SAAS,SAAS;AAChD,UAAI;AACF,YAAI,QAAQ,UAAU,KAAK,UAAU,QAAQ,KAAK,WAAW,KAAK,IAAI,MAAM;AAC5E,YAAI,MAAM,UAAU,KAAK,UAAU,QAAQ,KAAK,QAAQ;AACxD,aAAK,KAAK,QAAQ,OAAO,GAAG;AAC5B,aAAK,KAAK,QAAQ,KAAK;AACvB,aAAK,KAAK,KAAK;AACf,aAAK,WAAW;AAChB,eAAO;AAAA,MACT,SAAS,GAAG;AACV,aAAK,WAAW;AAChB,aAAK,KAAK,SAAS,CAAC;AACpB,aAAK,KAAK,OAAO;AAAA,MACnB;AAAA,IACF;AAEA,iBAAa,SAAS;AACtB,iBAAa,UAAU;AACvB,iBAAa,SAAS;AAEtB,WAAO,UAAU;AAAA;AAAA;;;ACvHjB;AAAA;AACA,QAAI,aAAa;AACjB,QAAI,eAAe;AAEnB,QAAI,aAAa;AAAA,MACf;AAAA,MAAS;AAAA,MAAS;AAAA,MAClB;AAAA,MAAS;AAAA,MAAS;AAAA,MAClB;AAAA,MAAS;AAAA,MAAS;AAAA,MAClB;AAAA,MAAS;AAAA,MAAS;AAAA,IACpB;AAEA,YAAQ,aAAa;AACrB,YAAQ,OAAO,WAAW;AAC1B,YAAQ,SAAS,aAAa;AAC9B,YAAQ,SAAS,aAAa;AAC9B,YAAQ,UAAU,aAAa;AAC/B,YAAQ,aAAa,SAAS,WAAW,MAAM;AAC7C,aAAO,IAAI,WAAW,IAAI;AAAA,IAC5B;AACA,YAAQ,eAAe,SAAS,aAAa,MAAM;AACjD,aAAO,IAAI,aAAa,IAAI;AAAA,IAC9B;AAAA;AAAA;;;ACrBA;AAAA;AAAA;AAEA,QAAM,mBAAmB,CAAC;AAE1B,qBAAiB,4BAA4B;AAAA,MAC3C,SAAS;AAAA,MACT,aAAa;AAAA,IACf;AAEA,qBAAiB,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA;AAMA,aAAS,SAAS,QAAQ;AACxB,aAAO,oBAAoB,KAAK,MAAM;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAEA,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,MAAM;AACZ,QAAM,EAAE,IAAI,IAAI;AAEhB,QAAM,mBAAmB;AACzB,QAAM,kBAAkB;AAKxB,QAAM,6BAA6B,KAAK,KAAK;AAG7C,QAAM,yBAAyB,KAAK,KAAK;AAEzC,QAAM,kBAAkB,KAAK,OAAO,gBAAgB,WAAW;AAC7D,WAAK,IAAI,EAAE;AAAA,QACT,KAAK,IAAI,SAAS,EAAE,IAAI;AAAA,QACxB,KAAK,IAAI,YAAY,EAAE,OAAO;AAAA,QAC9B,KAAK,IAAI,YAAY,EAAE,SAAS,CAAC,EAAE,MAAM,EACtC,SAAS;AAAA,QACZ,KAAK,IAAI,WAAW,EAAE,SAAS,CAAC,EAAE,OAAO,EACtC,SAAS;AAAA,MACd;AAAA,IACF,CAAC;AAED,aAAS,MAAM,KAAK;AAClB,aAAO,gBAAgB,OAAO;AAAA,QAC5B,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY,CAAC,GAAG,GAAG,KAAK,OAAO,GAAG,GAAG,CAAC;AAAA;AAAA,MACxC,GAAG,OAAO;AAAA,QACR,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,oBAAoB;AAC3B,YAAM,QAAQ,OAAO,WAAW,YAAY;AAC5C,YAAM,aAAa;AAEnB,UAAI,kBAAkB,MAAM,aAAa;AACzC,UAAI,mBAAmB,MAAM,cAAc;AAK3C,UAAI,iBAAiB,SAAS,IAAI;AAChC,cAAM,UAAU,OAAO,MAAM,KAAK,iBAAiB,MAAM;AACzD,gBAAQ,KAAK,CAAC;AACd,2BAAmB,OAAO,OAAO,CAAC,SAAS,gBAAgB,CAAC;AAAA,MAC9D;AAEA,UAAI,gBAAgB,SAAS,IAAI;AAC/B,cAAM,UAAU,OAAO,MAAM,KAAK,gBAAgB,MAAM;AACxD,gBAAQ,KAAK,CAAC;AACd,0BAAkB,OAAO,OAAO,CAAC,SAAS,eAAe,CAAC;AAAA,MAC5D;AAEA,aAAO;AAAA,QACL,WAAW,gBAAgB,SAAS,WAAW;AAAA,QAC/C,YAAY,iBAAiB,SAAS,WAAW;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,gBAAgB,SAAS;AAChC,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAEA,UAAI,OAAO,YAAY,YAAY,QAAQ,WAAW,GAAG;AACvD,cAAM,IAAI,MAAM,qFACQ,OAAO;AAAA,MACjC;AAEA,UAAI,qBAAqB;AACzB,UAAI;AACF,6BAAqB,IAAI,IAAI,OAAO;AAAA,MACtC,SAAS,KAAK;AACZ,cAAM,IAAI,MAAM,uCAAuC,OAAO;AAAA,MAChE;AACA,UAAI,CAAC,CAAC,UAAU,SAAS,EAAE,SAAS,mBAAmB,QAAQ,GAAG;AAChE,cAAM,IAAI,MAAM,oDAAoD,OAAO;AAAA,MAC7E;AACA,UAAI,mBAAmB,aAAa,aAAa;AAC/C,gBAAQ,KAAK,0KAEe;AAAA,MAC5B;AAAA,IACJ;AAEA,aAAS,kBAAkB,WAAW;AACpC,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAEA,UAAI,OAAO,cAAc,UAAU;AACjC,cAAM,IAAI,MAAM,gEACG;AAAA,MACrB;AAEA,UAAI,CAAC,gBAAgB,SAAS,SAAS,GAAG;AACxC,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,kBAAY,OAAO,KAAK,WAAW,WAAW;AAE9C,UAAI,UAAU,WAAW,IAAI;AAC3B,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAAA,IACF;AAEA,aAAS,mBAAmB,YAAY;AACtC,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,OAAO,eAAe,UAAU;AAClC,cAAM,IAAI,MAAM,8DACG;AAAA,MACrB;AAEA,UAAI,CAAC,gBAAgB,SAAS,UAAU,GAAG;AACzC,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AAEA,mBAAa,OAAO,KAAK,YAAY,WAAW;AAEhD,UAAI,WAAW,WAAW,IAAI;AAC5B,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAAA,IACF;AAUA,aAAS,6BAA6B,YAAY;AAChD,YAAM,YAAY,oBAAI,KAAK;AAC3B,gBAAU,WAAW,UAAU,WAAW,IAAI,UAAU;AACxD,aAAO,KAAK,MAAM,UAAU,QAAQ,IAAI,GAAI;AAAA,IAC9C;AAQA,aAAS,mBAAmB,YAAY;AACtC,UAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AACjC,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AAEA,UAAI,aAAa,GAAG;AAClB,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAIA,YAAM,yBAAyB,6BAA6B,sBAAsB;AAElF,UAAI,cAAc,wBAAwB;AACxC,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAAA,IACF;AAeA,aAAS,gBAAgB,UAAU,SAAS,WAAW,YAAY,iBAAiB,YAAY;AAC9F,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC7D;AAEA,UAAI,OAAO,aAAa,YAAY,SAAS,WAAW,GAAG;AACzD,cAAM,IAAI,MAAM,kFACiB,QAAQ;AAAA,MAC3C;AAEA,UAAI;AACF,YAAI,IAAI,QAAQ;AAAA,MAClB,SAAS,KAAK;AACZ,cAAM,IAAI,MAAM,kCAAkC,QAAQ;AAAA,MAC5D;AAEA,sBAAgB,OAAO;AACvB,wBAAkB,SAAS;AAC3B,yBAAmB,UAAU;AAE7B,mBAAa,OAAO,KAAK,YAAY,WAAW;AAEhD,UAAI,YAAY;AACd,2BAAmB,UAAU;AAAA,MAC/B,OAAO;AACL,qBAAa,6BAA6B,0BAA0B;AAAA,MACtE;AAEA,YAAM,SAAS;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAEA,YAAM,aAAa;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAEA,YAAM,MAAM,IAAI,KAAK;AAAA,QACnB;AAAA,QACA,SAAS;AAAA,QACT,YAAY,MAAM,UAAU;AAAA,MAC9B,CAAC;AAED,UAAI,oBAAoB,iBAAiB,0BAA0B,aAAa;AAC9E,eAAO;AAAA,UACL,eAAe,aAAa,MAAM,SAAS;AAAA,QAC7C;AAAA,MACF;AACA,UAAI,oBAAoB,iBAAiB,0BAA0B,SAAS;AAC1E,eAAO;AAAA,UACL,eAAe,aAAa;AAAA,UAC5B,cAAc,eAAe;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC9PA;AAAA;AAAA;AAgBA,QAAI,SAAS;AAEb,QAAI,UAAU;AACd,QAAI,WAAW,EAAE,aAAa,GAAG,UAAU,EAAE;AAC7C,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,iBAAiB;AACrB,QAAI,eAAe;AACnB,QAAI,eAAe;AAEnB,QAAI;AACJ,QAAI,QAAQ,IAAI,eAAe,KAAK;AAClC,eAAS,SAAS,GAAG,GAAG;AACtB,gBAAQ,KAAK,IAAI,OAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,WAAW,CAAC;AAClE,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,eAAS,SAAS,GAAG,GAAG;AAAE,eAAO;AAAA,MAAG;AAAA,IACtC;AAGA,aAAS,OAAO,GAAG;AACjB,UAAI,OAAO,MAAM,UAAU;AACzB,eAAO,OAAO,KAAK,GAAG,WAAW;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,KAAK,OAAO;AAC7B,UAAI,OAAO,OAAO,WAAW,UAAU,GAAG;AAC1C,WAAK,OAAO,KAAK;AACjB,aAAO,KAAK,OAAO;AAAA,IACrB;AAGA,aAAS,aAAa,MAAM,KAAK;AAC/B,aAAO,QAAQ,IAAI;AACnB,aAAO,OAAO,GAAG;AACjB,aAAO,OAAO,WAAW,UAAU,MAAM,GAAG,CAAC;AAAA,IAC/C;AAEA,aAAS,YAAY,KAAKC,OAAM,GAAG;AACjC,aAAO,OAAO,GAAG;AACjB,aAAO,QAAQA,KAAI;AACnB,UAAI,SAAS,OAAO,MAAM,CAAC;AAC3B,UAAI,IAAI,OAAO,MAAM,CAAC;AACtB,MAAAA,QAAO,OAAO,KAAKA,OAAM,OAAO;AAChC,UAAI,UAAU;AACd,UAAI,OAAO,OAAO,MAAM,CAAC;AACzB,aAAO,OAAO,SAAS,GAAG;AACxB,aAAK,YAAY,EAAE,SAAS,GAAG,CAAC;AAChC,YAAI,UAAU,KAAK,OAAO,OAAO,CAAC,GAAGA,OAAM,IAAI,CAAC,CAAC;AACjD,iBAAS,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;AAAA,MACpC;AAEA,aAAO,OAAO,UAAU,OAAO,MAAM,GAAG,CAAC,CAAC;AAAA,IAC5C;AAEA,aAAS,KAAK,MAAM,KAAKA,OAAM,KAAK;AAClC,aAAO,YAAY,aAAa,MAAM,GAAG,GAAGA,OAAM,GAAG;AAAA,IACvD;AAEA,aAAS,KAAK,MAAM,SAAS;AAC3B,UAAI,SAAS,OAAO,OAAO;AAAA,QACzB,OAAO,KAAK,uBAAuB,OAAO,MAAM,OAAO;AAAA,QACvD;AAAA,MACF,CAAC;AACD,aAAO,UAAU,MAAM,MAAM;AAC7B,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ;AAC5B,UAAI,IAAI,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC;AAC/C,QAAE,YAAY,OAAO,QAAQ,GAAG,CAAC;AACjC,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,QAAQ,MAAM;AAC/B,UAAI,MAAM,OAAO;AACjB,UAAI,cAAc;AAClB,UAAI,SAAS,cAAc;AACzB,uBAAe,IAAI,aAAa;AAChC,yBAAiB,OAAO;AAAA,MAC1B,WAAW,SAAS,cAAc;AAChC,uBAAe,OAAO;AACtB,yBAAiB,IAAI,aAAa;AAAA,MACpC,OAAO;AACL,cAAM,IAAI,MAAM,uBAAuB,eACvB,UAAU,eAAe,YAAY;AAAA,MACvD;AACA,aAAO;AAAA,QACL,QAAQ,IAAI,cAAc,OAAO,EAAE;AAAA,QACnC,SAAS,OAAO,OAAO;AAAA,UACrB,OAAO,KAAK,OAAO,UAAU,OAAO;AAAA,UACpC,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,UACf,aAAa,cAAc;AAAA;AAAA,UAC3B,aAAa,YAAY;AAAA;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS,wBAAwB,QAAQ,MAAM;AAC7C,UAAI,SAAS,EAAE,QAAQ,MAAM,SAAS,OAAO,MAAM,CAAC,EAAE;AACtD,UAAI,OAAO,KAAK;AACd,eAAO,SAAS,OAAO;AACvB,YAAI,OAAO,OAAO,WAAW,YAAY;AACvC,gBAAM,IAAI,MAAM,6BAA6B,aAAa,QAAQ;AAAA,QACpE;AAAA,MACF,WAAW,OAAO,IAAI;AACpB,iBAAS,UAAU,QAAQ,IAAI;AAAA,MACjC,WAAW,OAAO,OAAO,UAAU,QAAW;AAC5C,eAAO,SAAS,OAAO,OAAO,OAAO,KAAK;AAAA,MAC5C;AACA,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AACA,aAAO,UAAU,OAAO,MAAM;AAC9B,aAAO,WAAW,OAAO,OAAO;AAChC,UAAI,OAAO,YAAY;AACrB,eAAO,SAAS;AAAA,UAAK,OAAO;AAAA,UAAY,OAAO;AAAA,UAC1B,KAAK,QAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,UAAG;AAAA,QAAc;AAClE,eAAO,cAAc,OAAO,MAAM;AAAA,MACpC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,QAAQ,MAAM;AACnC,UAAI,CAAC,OAAO,YAAY;AACtB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,aAAO,cAAc,OAAO,UAAU;AAEtC,UAAI,cAAc,cAAc;AAChC,UAAI,SAAS,cAAc;AACzB,uBAAe,OAAO,WAAW,aAAa;AAC9C,uBAAe,iBAAiB,OAAO;AAAA,MACzC,WAAW,SAAS,cAAc;AAChC,uBAAe,eAAe,OAAO;AACrC,yBAAiB,OAAO,WAAW,aAAa;AAAA,MAClD,OAAO;AACL,cAAM,IAAI,MAAM,uBAAuB,eACvB,UAAU,eAAe,YAAY;AAAA,MACvD;AACA,aAAO,iBAAiB,YAAY;AACpC,aAAO,iBAAiB,YAAY;AACpC,aAAO,mBAAmB,cAAc;AACxC,aAAO;AAAA,QAAO;AAAA,QACA;AAAA,UAAK,OAAO;AAAA,UACP,OAAO,WAAW,cAAc,YAAY;AAAA,UAC5C,OAAO,OAAO;AAAA,YACZ,OAAO,KAAK,iBAAiB;AAAA,YAC7B;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACD;AAAA,QAAc;AAAA,MAAC;AAAA,IACpC;AAEA,aAAS,cAAc,QAAQ,MAAM,mBAAmB;AACtD,UAAI,mBAAmB;AACrB,YAAI,CAAC,WAAW,iBAAiB,GAAG;AAClC,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAAA,MACF;AAEA,UAAI,OAAO,KAAK;AACd,YAAI,OAAO,IAAI,WAAW,YAAY;AACpC,gBAAM,IAAI,MAAM,6BAA6B,aAAa,QAAQ;AAAA,QACpE;AACA,eAAO,OAAO,cAAc,OAAO,GAAG;AAAA,MACxC;AAEA,UAAI,CAAC,OAAO,YAAY;AAEtB,YAAI,CAAC,mBAAmB;AACtB,cAAI,MAAM,OAAO,UAAU,OAAO,OAAO,OAAO,KAAK;AAAA,QACvD,OAAO;AACL,cAAI,MAAM,kBAAkB,OAAO,KAAK;AAAA,QAC1C;AACA,YAAI,CAAC,KAAK;AACR,gBAAM,IAAI,MAAM,2BAA2B,OAAO,QAAQ,IAAI;AAAA,QAChE;AACA,eAAO;AAAA,MACT;AAEA,aAAO,cAAc,QAAQ,IAAI;AAAA,IACnC;AAEA,aAAS,kBAAkB,QAAQ,MAAM,mBAAmB;AAC1D,UAAI,CAAC,OAAO,MAAM;AAChB,cAAM,IAAI,MAAM,uCAAuC,OAAO,OAAO;AAAA,MACvE;AACA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,OAAO,YAAY,UAAU;AAE/B,YAAI,IAAI,wBAAwB,QAAQ,MAAM,iBAAiB;AAC/D,kBAAU,KAAK,UAAU,EAAE,OAAO;AAClC,oBAAY,KAAK,SAAS,EAAE,OAAO;AACnC,iBAAS,EAAE;AAAA,MACb,WAAW,OAAO,YAAY,aAAa;AAEzC,kBAAU,OAAO,KAAK,+BAA+B;AACrD,oBAAY,OAAO,KAAK,2BAA2B;AACnD,iBAAS,cAAc,QAAQ,MAAM,iBAAiB;AAAA,MACxD,OAAO;AACL,cAAM,IAAI,MAAM,oCAAoC,OAAO,OAAO;AAAA,MACpE;AACA,UAAI,MAAM,aAAa,OAAO,MAAM,MAAM;AAC1C,UAAI,SAAS;AAAA,QACX,KAAK,YAAY,KAAK,SAAS,UAAU;AAAA,QACzC,OAAO,YAAY,KAAK,WAAW,YAAY;AAAA,MACjD;AACA,aAAO,OAAO,OAAO,GAAG;AACxB,aAAO,cAAc,OAAO,KAAK;AACjC,aAAO;AAAA,IACT;AAGA,aAAS,YAAY,QAAQ;AAC3B,UAAI,SAAS,CAAC;AAEd,aAAO,UAAU,OAAO,WAAW;AACnC,aAAO,KAAK,SAAS,OAAO,IAAI,EAAE;AAClC,UAAI,MAAM,OAAO,EAAE,GAAG;AACpB,eAAO,KAAK;AAAA,MACd;AACA,UAAI,WAAW,SAAS,OAAO,OAAO;AACtC,UAAI,OAAO,YAAY,aAAa;AAClC,oBAAY;AAAA,MACd;AACA,UAAI,OAAO,MAAM,UAAU;AACzB,cAAM,IAAI,MAAM,6CAA6C,QAAQ;AAAA,MACvE;AAEA,UAAI,OAAO,MAAM;AACf,eAAO,OAAO,OAAO,OAAO,IAAI;AAChC,YAAI,OAAO,KAAK,WAAW,YAAY;AACrC,gBAAM,IAAI,MAAM,gCAAgC,aAAa,QAAQ;AAAA,QACvE;AAAA,MACF;AACA,aAAO,QAAQ,OAAO;AACtB,UAAI,OAAO,KAAK;AACd,eAAO,MAAM,OAAO,OAAO,GAAG;AAAA,MAChC,OAAO;AACL,eAAO,aAAa,OAAO;AAC3B,YAAI,CAAC,OAAO,YAAY;AACtB,iBAAO,SAAS,OAAO;AAAA,QACzB;AACA,YAAI,OAAO,YAAY,aAAa;AAClC,iBAAO,WAAW,OAAO,YAAY;AAAA,QACvC;AACA,YAAI,OAAO,IAAI;AACb,iBAAO,KAAK,OAAO,OAAO,EAAE;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,OAAO,YAAY;AACrB,eAAO,aAAa,OAAO,OAAO,UAAU;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,MAAM,SAAS;AACpC,UAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,UAAI,IAAI,MAAM,WAAW,MAAM,SAAS,GAAG,CAAC;AAC5C,UAAI,MAAM,IAAI,WAAW,cACjB,IAAI,WAAc,UAAU,YAAc,YAAY;AAC9D,YAAM,YAAY,GAAG,MAAM,SAAS,GAAG,CAAC;AACxC,aAAO,UAAU,SAAS,KAAK;AAC/B,aAAO;AAAA,IACT;AAIA,aAAS,WAAW,QAAQ,QAAQ;AAClC,UAAI,OAAO,OAAO,WAAW,IAAI,CAAC;AAClC,aAAO,OAAO,OAAO,MAAM,GAAG,UAAU;AACxC,aAAO,KAAK,OAAO,WAAW,YAAY,CAAC;AAC3C,aAAO,QAAQ,OAAO,MAAM,IAAI,KAAK,IAAI;AACzC,aAAO,KAAK;AAAA,IACd;AAEA,aAAS,YAAY,MAAM,SAAS;AAClC,UAAI,UAAU,SAAS,OAAO;AAC9B,UAAI,MAAM,KAAK,WAAW,GAAG,OAAO;AACpC,UAAI,MAAM,UAAU,KAAK,QAAQ;AAC/B,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AACA,aAAO,WAAW,KAAK,MAAM,GAAG,UAAU,GAAG,CAAC;AAC9C,UAAI,WAAW,OAAO,MAAM,GAAG;AAC/B,eAAS,KAAK,CAAC;AACf,UAAI,SAAS,QAAQ,KAAK,MAAM,SAAS,UAAU,GAAG,CAAC,MAAM,GAAG;AAC9D,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AACA,aAAO,KAAK,MAAM,UAAU,GAAG;AAAA,IACjC;AAEA,aAAS,MAAM,MAAM,MAAM;AACzB,UAAI,IAAI,KAAK,SAAS;AACtB,aAAM,KAAK,GAAG;AACZ,YAAI,KAAK,CAAC,GAAG;AACX,cAAI,MAAM;AACR,gBAAI,KAAK,CAAC,MAAM,GAAG;AACjB,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YAC/D;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,CAAC,MAAM,GAAG;AACjB,oBAAM,IAAI,MAAM,6CAA6C;AAAA,YAC/D;AAAA,UACF;AACA,iBAAO,KAAK,MAAM,GAAG,CAAC;AAAA,QACxB;AACA,UAAE;AAAA,MACJ;AACA,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAEA,aAAS,cAAc,KAAK,SAAS,QAAQ,QAAQ,MAAM;AACzD,aAAO,WAAW,MAAM;AACxB,UAAI,QAAQ,cAAc,IAAI,OAAO,OAAO;AAC5C,UAAI,MAAM,OAAO,iBAAiB,SAAS,IAAI,KAAK,KAAK;AACzD,UAAI,WAAW,OAAO,MAAM,OAAO,SAAS,UAAU,CAAC;AACvD,UAAI,OAAO,IAAI,OAAO,OAAO,MAAM,GAAG,OAAO,SAAS,UAAU,CAAC;AACjE,aAAO,OAAO,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;AACxC,aAAO,aAAa,IAAI;AACxB,UAAI,OAAO,YAAY,aAAa;AAClC,eAAO,YAAY,MAAM,OAAO,OAAO;AAAA,MACzC;AACA,aAAO,MAAM,MAAM,IAAI;AAAA,IACzB;AAkBA,aAAS,QAAQ,QAAQ,QAAQ,mBAAmB;AAClD,UAAI,SAAS,YAAY,MAAM;AAC/B,UAAI,OAAO,YAAY,aAAa;AAClC,YAAI,eAAe,WAAW,QAAQ,MAAM;AAC5C,iBAAS,OAAO,MAAM,YAAY;AAAA,MACpC;AACA,UAAI,MAAM,kBAAkB,QAAQ,cAAc,iBAAiB;AACnE,UAAI,QAAQ;AACZ,UAAI,SAAS,OAAO,MAAM,CAAC;AAE3B,UAAI,YAAY,OAAO;AACvB,UAAI,OAAO,YAAY,aAAa;AAClC,qBAAa;AAAA,MACf;AAEA,eAAS,IAAI,GAAG,QAAQ,OAAO,QAAQ,EAAE,GAAG;AAC1C,YAAI,MAAM,QAAQ;AAClB,YAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAQ;AAC3D,gBAAM,IAAI,MAAM,mBAAmB;AAAA,QACrC;AACA,cAAM,KAAK,IAAI,KAAK,OAAO,MAAM;AACjC,YAAI,MAAM,SAAS,YAAY;AAC7B,gBAAM,IAAI,MAAM,iCAAiC,CAAC;AAAA,QACpD;AACA,YAAI,QAAQ;AAAA,UAAc;AAAA,UAAK;AAAA,UAAG,OAAO,MAAM,OAAO,GAAG;AAAA,UAC/B;AAAA,UAAQ,OAAO,OAAO;AAAA,QAAM;AACtD,iBAAS,OAAO,OAAO,CAAC,QAAQ,KAAK,CAAC;AACtC,gBAAQ;AAAA,MACV;AACA,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,KAAK,SAAS,QAAQ,KAAK,QAAQ,MAAM;AAC9D,aAAO,WAAW,MAAM;AACxB,YAAM,OAAO;AACb,UAAI,QAAQ,cAAc,IAAI,OAAO,OAAO;AAC5C,UAAI,MAAM,OAAO,eAAe,SAAS,IAAI,KAAK,KAAK;AAEvD,UAAI,aAAa,CAAC;AAClB,UAAI,UAAU,SAAS,OAAO,OAAO;AACrC,UAAI,UAAU,OAAO,MAAM,MAAM,OAAO;AACxC,cAAQ,KAAK,CAAC;AAEd,UAAI,OAAO,YAAY,aAAa;AAClC,gBAAQ,YAAY,KAAK,GAAG,OAAO;AACnC,eAAO,WAAW,OAAO;AACzB,mBAAW,KAAK,IAAI,OAAO,OAAO,CAAC;AACnC,mBAAW,KAAK,IAAI,OAAO,MAAM,CAAC;AAElC,YAAI,CAAC,QAAQ,QAAQ,SAAS,OAAO,SAAS,OAAO,IAAI;AACvD,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAChD;AAAA,MACF,OAAO;AACL,mBAAW,KAAK,IAAI,OAAO,MAAM,CAAC;AAClC,gBAAQ,YAAY,OAAO,IAAI,GAAG,GAAG,CAAC;AACtC,eAAO,WAAW,OAAO;AACzB,mBAAW,KAAK,IAAI,OAAO,OAAO,CAAC;AAAA,MACrC;AAEA,UAAI,MAAM;AACV,UAAI,MAAM,IAAI,WAAW;AACzB,UAAI,IAAI,WAAW,YAAY;AAC7B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AACA,iBAAW,KAAK,GAAG;AACnB,aAAO,OAAO,aAAa,OAAO,OAAO,UAAU,CAAC;AAAA,IACtD;AAEA,aAAS,YAAY,QAAQ;AAC3B,UAAI,OAAO,OAAO,MAAM,CAAC;AACzB,UAAI,QAAQ,OAAO,KAAK,OAAO,SAAS,CAAC,CAAC;AAC1C,UAAI,MAAM,SAAS,KAAK;AACtB,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AACA,WAAK,YAAY,OAAO,IAAI,GAAG,CAAC;AAChC,WAAK,YAAY,MAAM,QAAQ,GAAG,CAAC;AACnC,aAAO,OAAO,OAAO,CAAC,OAAO,MAAM,MAAM,KAAK,CAAC;AAAA,IACjD;AAgBA,aAAS,QAAQ,QAAQ,QAAQ,mBAAmB;AAClD,UAAI,CAAC,OAAO,SAAS,MAAM,GAAG;AAC5B,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACpD;AACA,UAAI,SAAS,YAAY,MAAM;AAC/B,UAAI,CAAC,OAAO,MAAM;AAChB,eAAO,OAAO,OAAO,YAAY,UAAU;AAAA,MAC7C;AAEA,UAAI;AACJ,UAAI,OAAO,YAAY,aAAa;AAElC,YAAI,OAAO,cAAc,CAAC,OAAO,OAAO;AACtC,iBAAO,QAAQ,OAAO,WAAW,aAAa;AAAA,QAChD;AACA,iBAAS,YAAY,MAAM;AAAA,MAC7B,OAAO;AAEL,iBAAS,OAAO,MAAM,CAAC;AAAA,MACzB;AAEA,UAAI,MAAM,kBAAkB,QAAQ,cAAc,iBAAiB;AACnE,UAAI,QAAQ;AACZ,UAAI,UAAU,SAAS,OAAO,OAAO;AACrC,UAAI,WAAW;AACf,UAAI,OAAO,YAAY,aAAa;AAClC,oBAAY;AAAA,MACd;AACA,UAAI,MAAM,MAAM,SAAS,OAAO,KAAK,EAAE,CAAC,IAAI,IAAI,SAAS,OAAO,KAAK,EAAE;AAEvE,UAAI,UAAU;AACd,UAAI,OAAO;AACX,aAAO,CAAC,MAAM;AAEZ,YAAI,YAAY,KAAK,IAAI,OAAO,KAAK,WAAW,GAAG,GAAG;AACtD,YAAI,OAAO,YAAY,aAAa;AAClC,sBAAY,KAAK,KAAK,KAAM,UAAU,KAAM,GAAG,SAAS;AAAA,QAC1D;AACA,YAAI,MAAM,KAAK,cAAc,GAAG;AAC9B,YAAE;AAAA,QACJ;AACA,eAAO;AAEP,YAAI,MAAM,QAAQ,OAAO,KAAK,WAAW;AACzC,YAAI,OAAO,YAAY,aAAa;AAGlC,iBAAO,MAAM,OAAO;AAAA,QACtB,OAAO;AACL,iBAAO,OAAO,OAAO;AAAA,QACvB;AACA,eAAO,QAAQ,OAAO;AACtB,YAAI,QAAQ;AAAA,UAAc;AAAA,UAAK;AAAA,UAAS,OAAO,MAAM,OAAO,GAAG;AAAA,UACrC;AAAA,UAAW;AAAA,UAAQ;AAAA,QAAI;AACjD,iBAAS,OAAO,OAAO,CAAC,QAAQ,KAAK,CAAC;AAEtC,gBAAQ;AACR,UAAE;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAGA,aAAS,WAAW,QAAQ;AAC1B,aAAO,OAAO,WAAY;AAAA,IAC3B;AAED,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC/gBA;AAAA;AAAA;AAEA,QAAM,SAAS;AACf,QAAM,MAAM;AAEZ,QAAM,UAAU,SAAS,eAAe,UAAU,SAAS,iBAAiB;AAC1E,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAEA,UAAI,OAAO,kBAAkB,UAAU;AACrC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AAEA,UAAI,OAAO,KAAK,eAAe,WAAW,EAAE,WAAW,IAAI;AACzD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,OAAO,aAAa,UAAU;AAChC,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAEA,UAAI,OAAO,KAAK,UAAU,WAAW,EAAE,SAAS,IAAI;AAClD,cAAM,IAAI,MAAM,4DACF;AAAA,MAChB;AAEA,UAAI,OAAO,YAAY,YAAY,CAAC,OAAO,SAAS,OAAO,GAAG;AAC5D,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAEA,UAAI,OAAO,YAAY,YAAY,mBAAmB,QAAQ;AAC5D,kBAAU,OAAO,KAAK,OAAO;AAAA,MAC/B;AAEA,YAAM,aAAa,OAAO,WAAW,YAAY;AACjD,YAAM,iBAAiB,WAAW,aAAa;AAE/C,YAAM,OAAO,OAAO,YAAY,EAAE,EAAE,SAAS,WAAW;AAExD,YAAM,aAAa,IAAI,QAAQ,SAAS;AAAA,QACtC,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,YAAY;AAAA,QACZ;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;AC7DA;AAAA;AAAA;AAEA,aAAS,aAAa,SAAS,YAAY,SAAS,MAAM,UAAU;AAClE,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAE9C,WAAK,OAAO,KAAK,YAAY;AAC7B,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,WAAW;AAAA,IAClB;AAEA,mBAAgB,SAAS,cAAc,KAAK;AAE5C,WAAO,UAAU;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfjB,QAAA,OAAA,aAAA,cAAA;AACA,QAAA,QAAA,aAAA,eAAA;AAOO,mBAAe,SAAS,QAAgB;AAC9C,UAAI,SAAS;AACb,YAAM,SAAmB,CAAA;AACzB,uBAAiB,SAAS,QAAQ;AACjC,kBAAU,MAAM;AAChB,eAAO,KAAK,KAAK;;AAElB,aAAO,OAAO,OAAO,QAAQ,MAAM;IACpC;AARA,YAAA,WAAA;AAWO,mBAAe,KAAK,QAAgB;AAC1C,YAAM,MAAM,MAAM,SAAS,MAAM;AACjC,YAAM,MAAM,IAAI,SAAS,MAAM;AAC/B,UAAI;AACH,eAAO,KAAK,MAAM,GAAG;eACb,MAAe;AACvB,cAAM,MAAM;AACZ,YAAI,WAAW,YAAY,GAAG;AAC9B,cAAM;;IAER;AAVA,YAAA,OAAA;AAYA,aAAgB,IACf,KACA,OAA6B,CAAA,GAAE;AAE/B,YAAM,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI;AACjD,YAAMC,QAAO,KAAK,WAAW,QAAQ,IAAI,QAAQ,MAAM,QACtD,KACA,IAAI;AAEL,YAAM,UAAU,IAAI,QAA8B,CAAC,SAAS,WAAU;AACrE,QAAAA,KACE,KAAK,YAAY,OAAO,EACxB,KAAK,SAAS,MAAM,EACpB,IAAG;MACN,CAAC;AACD,MAAAA,KAAI,OAAO,QAAQ,KAAK,KAAK,OAAO;AACpC,aAAOA;IACR;AAjBA,YAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BA,QAAA,MAAA,aAAA,aAAA;AAEA,QAAA,OAAA,aAAA,cAAA;AACA,QAAA,UAAA;AAGA,iBAAA,mBAAA,OAAA;AAeA,QAAM,WAAW,OAAO,wBAAwB;AAQhD,QAAsB,QAAtB,cAAoC,KAAK,MAAK;MAO7C,YAAY,MAAwB;AACnC,cAAM,IAAI;AACV,aAAK,QAAQ,IAAI,CAAA;MAClB;;;;MAUA,iBAAiB,SAA0B;AAC1C,YAAI,SAAS;AAIZ,cAAI,OAAQ,QAAgB,mBAAmB,WAAW;AACzD,mBAAO,QAAQ;;AAMhB,cAAI,OAAO,QAAQ,aAAa,UAAU;AACzC,mBAAO,QAAQ,aAAa;;;AAO9B,cAAM,EAAE,MAAK,IAAK,IAAI,MAAK;AAC3B,YAAI,OAAO,UAAU;AAAU,iBAAO;AACtC,eAAO,MACL,MAAM,IAAI,EACV,KACA,CAAC,MACA,EAAE,QAAQ,YAAY,MAAM,MAC5B,EAAE,QAAQ,aAAa,MAAM,EAAE;MAEnC;;;;;;;MAQQ,iBAAiB,MAAY;AAIpC,YAAI,KAAK,eAAe,YAAY,KAAK,oBAAoB,UAAU;AACtE,iBAAO;;AAKR,YAAI,CAAC,KAAK,QAAQ,IAAI,GAAG;AAExB,eAAK,QAAQ,IAAI,IAAI,CAAA;;AAEtB,cAAM,aAAa,IAAI,IAAI,OAAO,EAAE,UAAU,MAAK,CAAE;AACpD,aAAK,QAAQ,IAAI,EAAmB,KAAK,UAAU;AAEpD,aAAK;AACL,eAAO;MACR;MAEQ,iBAAiB,MAAc,QAAyB;AAC/D,YAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,WAAW,MAAM;AAC3C;;AAED,cAAM,UAAU,KAAK,QAAQ,IAAI;AACjC,cAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,YAAI,UAAU,IAAI;AACjB,kBAAQ,OAAO,OAAO,CAAC;AAEvB,eAAK;AACL,cAAI,QAAQ,WAAW,GAAG;AAEzB,mBAAO,KAAK,QAAQ,IAAI;;;MAG3B;;;MAIA,QAAQ,SAAyB;AAChC,cAAM,iBACL,OAAO,QAAQ,mBAAmB,YAC/B,QAAQ,iBACR,KAAK,iBAAiB,OAAO;AACjC,YAAI,gBAAgB;AAEnB,iBAAO,QAAA,MAAW,UAAU,QAAQ,KAAK,MAAM,OAAO;;AAGvD,eAAO,MAAM,QAAQ,OAAO;MAC7B;MAEA,aACC,KACA,SACA,IAA2C;AAE3C,cAAM,cAAc;UACnB,GAAG;UACH,gBAAgB,KAAK,iBAAiB,OAAO;;AAE9C,cAAM,OAAO,KAAK,QAAQ,WAAW;AACrC,cAAM,aAAa,KAAK,iBAAiB,IAAI;AAC7C,gBAAQ,QAAO,EACb,KAAK,MAAM,KAAK,QAAQ,KAAK,WAAW,CAAC,EACzC,KACA,CAAC,WAAU;AACV,eAAK,iBAAiB,MAAM,UAAU;AACtC,cAAI,kBAAkB,KAAK,OAAO;AACjC,gBAAI;AAEH,qBAAO,OAAO,WAAW,KAAK,WAAW;qBACjC,KAAc;AACtB,qBAAO,GAAG,GAAY;;;AAGxB,eAAK,QAAQ,EAAE,gBAAgB;AAE/B,gBAAM,aAAa,KAAK,SAAS,EAAE;QACpC,GACA,CAAC,QAAO;AACP,eAAK,iBAAiB,MAAM,UAAU;AACtC,aAAG,GAAG;QACP,CAAC;MAEJ;MAEA,mBAAgB;AACf,cAAM,SAAS,KAAK,QAAQ,EAAE;AAC9B,aAAK,QAAQ,EAAE,gBAAgB;AAC/B,YAAI,CAAC,QAAQ;AACZ,gBAAM,IAAI,MACT,oDAAoD;;AAGtD,eAAO;MACR;MAEA,IAAI,cAAW;AACd,eACC,KAAK,QAAQ,EAAE,gBACd,KAAK,aAAa,WAAW,MAAM;MAEtC;MAEA,IAAI,YAAY,GAAS;AACxB,YAAI,KAAK,QAAQ,GAAG;AACnB,eAAK,QAAQ,EAAE,cAAc;;MAE/B;MAEA,IAAI,WAAQ;AACX,eACC,KAAK,QAAQ,EAAE,aACd,KAAK,iBAAgB,IAAK,WAAW;MAExC;MAEA,IAAI,SAAS,GAAS;AACrB,YAAI,KAAK,QAAQ,GAAG;AACnB,eAAK,QAAQ,EAAE,WAAW;;MAE5B;;AApLD,YAAA,QAAA;;;;;;;;;;;;;AC7BA,QAAA,UAAA,gBAAA,aAAA;AAIA,QAAM,SAAQ,GAAA,QAAA,SAAY,wCAAwC;AAQlE,aAAgB,mBACf,QAAgB;AAEhB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AAKtC,YAAI,gBAAgB;AACpB,cAAM,UAAoB,CAAA;AAE1B,iBAAS,OAAI;AACZ,gBAAM,IAAI,OAAO,KAAI;AACrB,cAAI;AAAG,mBAAO,CAAC;;AACV,mBAAO,KAAK,YAAY,IAAI;QAClC;AAEA,iBAAS,UAAO;AACf,iBAAO,eAAe,OAAO,KAAK;AAClC,iBAAO,eAAe,SAAS,OAAO;AACtC,iBAAO,eAAe,YAAY,IAAI;QACvC;AAEA,iBAAS,QAAK;AACb,kBAAO;AACP,gBAAM,OAAO;AACb,iBACC,IAAI,MACH,0DAA0D,CAC1D;QAEH;AAEA,iBAAS,QAAQ,KAAU;AAC1B,kBAAO;AACP,gBAAM,cAAc,GAAG;AACvB,iBAAO,GAAG;QACX;AAEA,iBAAS,OAAO,GAAS;AACxB,kBAAQ,KAAK,CAAC;AACd,2BAAiB,EAAE;AAEnB,gBAAM,WAAW,OAAO,OAAO,SAAS,aAAa;AACrD,gBAAM,eAAe,SAAS,QAAQ,UAAU;AAEhD,cAAI,iBAAiB,IAAI;AAExB,kBAAM,8CAA8C;AACpD,iBAAI;AACJ;;AAGD,gBAAM,cAAc,SAClB,MAAM,GAAG,YAAY,EACrB,SAAS,OAAO,EAChB,MAAM,MAAM;AACd,gBAAM,YAAY,YAAY,MAAK;AACnC,cAAI,CAAC,WAAW;AACf,mBAAO,QAAO;AACd,mBAAO,OACN,IAAI,MAAM,gDAAgD,CAAC;;AAG7D,gBAAM,iBAAiB,UAAU,MAAM,GAAG;AAC1C,gBAAM,aAAa,CAAC,eAAe,CAAC;AACpC,gBAAM,aAAa,eAAe,MAAM,CAAC,EAAE,KAAK,GAAG;AACnD,gBAAM,UAA+B,CAAA;AACrC,qBAAW,UAAU,aAAa;AACjC,gBAAI,CAAC;AAAQ;AACb,kBAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,gBAAI,eAAe,IAAI;AACtB,qBAAO,QAAO;AACd,qBAAO,OACN,IAAI,MACH,gDAAgD,MAAM,GAAG,CACzD;;AAGH,kBAAM,MAAM,OAAO,MAAM,GAAG,UAAU,EAAE,YAAW;AACnD,kBAAM,QAAQ,OAAO,MAAM,aAAa,CAAC,EAAE,UAAS;AACpD,kBAAM,UAAU,QAAQ,GAAG;AAC3B,gBAAI,OAAO,YAAY,UAAU;AAChC,sBAAQ,GAAG,IAAI,CAAC,SAAS,KAAK;uBACpB,MAAM,QAAQ,OAAO,GAAG;AAClC,sBAAQ,KAAK,KAAK;mBACZ;AACN,sBAAQ,GAAG,IAAI;;;AAGjB,gBAAM,oCAAoC,WAAW,OAAO;AAC5D,kBAAO;AACP,kBAAQ;YACP,SAAS;cACR;cACA;cACA;;YAED;WACA;QACF;AAEA,eAAO,GAAG,SAAS,OAAO;AAC1B,eAAO,GAAG,OAAO,KAAK;AAEtB,aAAI;MACL,CAAC;IACF;AA3GA,YAAA,qBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZA,QAAA,MAAA,aAAA,aAAA;AACA,QAAA,MAAA,aAAA,aAAA;AAEA,QAAA,WAAA,gBAAA,gBAAA;AACA,QAAA,UAAA,gBAAA,aAAA;AACA,QAAA,eAAA;AACA,QAAA,QAAA;AACA,QAAA,yBAAA;AAGA,QAAM,SAAQ,GAAA,QAAA,SAAY,mBAAmB;AAE7C,QAAM,6BAA6B,CAGlC,YACG;AACH,UACC,QAAQ,eAAe,UACvB,QAAQ,QACR,CAAC,IAAI,KAAK,QAAQ,IAAI,GACrB;AACD,eAAO;UACN,GAAG;UACH,YAAY,QAAQ;;;AAGtB,aAAO;IACR;AAiCA,QAAa,kBAAb,cAAyD,aAAA,MAAK;MAO7D,YAAY,OAAkB,MAAkC;AAC/D,cAAM,IAAI;AACV,aAAK,UAAU,EAAE,MAAM,OAAS;AAChC,aAAK,QAAQ,OAAO,UAAU,WAAW,IAAI,MAAA,IAAI,KAAK,IAAI;AAC1D,aAAK,gBAAe,6BAAM,YAAW,CAAA;AACrC,cAAM,6CAA6C,KAAK,MAAM,IAAI;AAGlE,cAAM,QAAQ,KAAK,MAAM,YAAY,KAAK,MAAM,MAAM,QACrD,YACA,EAAE;AAEH,cAAM,OAAO,KAAK,MAAM,OACrB,SAAS,KAAK,MAAM,MAAM,EAAE,IAC5B,KAAK,MAAM,aAAa,WACxB,MACA;AACH,aAAK,cAAc;;UAElB,eAAe,CAAC,UAAU;UAC1B,GAAI,OAAO,KAAK,MAAM,SAAS,IAAI;UACnC;UACA;;MAEF;;;;;MAMA,MAAM,QACL,KACA,MAAsB;AAEtB,cAAM,EAAE,MAAK,IAAK;AAElB,YAAI,CAAC,KAAK,MAAM;AACf,gBAAM,IAAI,UAAU,oBAAoB;;AAIzC,YAAI;AACJ,YAAI,MAAM,aAAa,UAAU;AAChC,gBAAM,6BAA6B,KAAK,WAAW;AACnD,mBAAS,IAAI,QAAQ,2BAA2B,KAAK,WAAW,CAAC;eAC3D;AACN,gBAAM,6BAA6B,KAAK,WAAW;AACnD,mBAAS,IAAI,QAAQ,KAAK,WAAW;;AAGtC,cAAM,UACL,OAAO,KAAK,iBAAiB,aAC1B,KAAK,aAAY,IACjB,EAAE,GAAG,KAAK,aAAY;AAC1B,cAAM,OAAO,IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,MAAM,KAAK;AAC7D,YAAI,UAAU,WAAW,IAAI,IAAI,KAAK,IAAI;;AAG1C,YAAI,MAAM,YAAY,MAAM,UAAU;AACrC,gBAAM,OAAO,GAAG,mBACf,MAAM,QAAQ,CACd,IAAI,mBAAmB,MAAM,QAAQ,CAAC;AACvC,kBAAQ,qBAAqB,IAAI,SAAS,OAAO,KAChD,IAAI,EACH,SAAS,QAAQ,CAAC;;AAGrB,gBAAQ,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI;AAEnC,YAAI,CAAC,QAAQ,kBAAkB,GAAG;AACjC,kBAAQ,kBAAkB,IAAI,KAAK,YAChC,eACA;;AAEJ,mBAAW,QAAQ,OAAO,KAAK,OAAO,GAAG;AACxC,qBAAW,GAAG,IAAI,KAAK,QAAQ,IAAI,CAAC;;;AAGrC,cAAM,wBAAuB,GAAA,uBAAA,oBAAmB,MAAM;AAEtD,eAAO,MAAM,GAAG,OAAO;CAAM;AAE7B,cAAM,EAAE,SAAS,SAAQ,IAAK,MAAM;AACpC,YAAI,KAAK,gBAAgB,OAAO;AAChC,aAAK,KAAK,gBAAgB,SAAS,GAAG;AAEtC,YAAI,QAAQ,eAAe,KAAK;AAC/B,cAAI,KAAK,UAAU,MAAM;AAEzB,cAAI,KAAK,gBAAgB;AAGxB,kBAAM,oCAAoC;AAC1C,mBAAO,IAAI,QAAQ;cAClB,GAAG,KACF,2BAA2B,IAAI,GAC/B,QACA,QACA,MAAM;cAEP;aACA;;AAGF,iBAAO;;AAcR,eAAO,QAAO;AAEd,cAAM,aAAa,IAAI,IAAI,OAAO,EAAE,UAAU,MAAK,CAAE;AACrD,mBAAW,WAAW;AAGtB,YAAI,KAAK,UAAU,CAAC,MAAiB;AACpC,gBAAM,2CAA2C;AACjD,WAAA,GAAA,SAAA,SAAO,EAAE,cAAc,MAAM,IAAI,CAAC;AAKlC,YAAE,KAAK,QAAQ;AACf,YAAE,KAAK,IAAI;QACZ,CAAC;AAED,eAAO;MACR;;AA9IO,oBAAA,YAAY,CAAC,QAAQ,OAAO;AADvB,YAAA,kBAAA;AAkJb,aAAS,OAAO,QAAkC;AACjD,aAAO,OAAM;IACd;AAEA,aAAS,KACR,QACG,MAAO;AAIV,YAAM,MAAM,CAAA;AAGZ,UAAI;AACJ,WAAK,OAAO,KAAK;AAChB,YAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,cAAI,GAAG,IAAI,IAAI,GAAG;;;AAGpB,aAAO;IACR;;;;;ACnOA;AAAA;AAAA;AAEA,QAAM,MAAM;AACZ,QAAM,QAAQ;AAEd,QAAM,eAAe;AACrB,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,mBAAmB;AACzB,QAAM,kBAAkB;AAGxB,QAAM,cAAc;AAEpB,QAAI,YAAY;AAChB,QAAI;AAEJ,aAAS,aAAa;AAAA,IAEtB;AAQA,eAAW,UAAU,eAAe,SAAS,QAAQ;AACnD,UAAI,WAAW,MAAM;AACnB,oBAAY;AACZ;AAAA,MACF;AAEA,UAAI,OAAO,WAAW,eACnB,OAAO,WAAW,YAClB,OAAO,WAAW,GAAG;AACtB,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,kBAAY;AAAA,IACd;AAYA,eAAW,UAAU,kBAAkB,SAAS,SAAS,WAAW,YAAY;AAC5E,UAAI,UAAU,WAAW,KAAK,UAAU,CAAC,MAAM,MAAM;AACnD,uBAAe;AACf;AAAA,MACF;AAEA,kBAAY,gBAAgB,OAAO;AACnC,kBAAY,kBAAkB,SAAS;AACvC,kBAAY,mBAAmB,UAAU;AAEzC,qBAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAiBF,eAAW,UAAU,yBAAyB,SAAS,cAAc,SAAS,SAAS;AACnF,UAAI,CAAC,gBAAgB,CAAC,aAAa,UAAU;AAC3C,cAAM,IAAI,MAAM,4DACA;AAAA,MAClB;AAEA,UAAI,OAAO,aAAa,aAAa,YAClC,aAAa,SAAS,WAAW,GAAG;AACrC,cAAM,IAAI,MAAM,8DACA;AAAA,MAClB;AAEA,UAAI,SAAS;AAEX,YAAI,OAAO,iBAAiB,YAAY,CAAC,aAAa,QACnD,CAAC,aAAa,KAAK,UACnB,CAAC,aAAa,KAAK,MAAM;AAC1B,gBAAM,IAAI,MAAM,wFACwC;AAAA,QAC1D;AAAA,MACF;AAEA,UAAI,mBAAmB;AACvB,UAAI,sBAAsB;AAC1B,UAAI,aAAa;AACjB,UAAI,eAAe,CAAC;AACpB,UAAI,kBAAkB,iBAAiB,0BAA0B;AACjE,UAAI,UAAU,iBAAiB,iBAAiB;AAChD,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,SAAS;AACX,cAAM,kBAAkB;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM,aAAa,OAAO,KAAK,OAAO;AACtC,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,gBAAM,YAAY,WAAW,CAAC;AAC9B,cAAI,CAAC,gBAAgB,SAAS,SAAS,GAAG;AACxC,kBAAM,IAAI,MAAM,MAAO,YAAY,qDACH,gBAAgB,KAAK,MAAQ,IAC3D,KAAM;AAAA,UACV;AAAA,QACF;AAEA,YAAI,QAAQ,SAAS;AACnB,yBAAe,QAAQ;AACvB,cAAI,aAAa,OAAO,KAAK,YAAY,EACpC,OAAO,SAAU,QAAQ;AACxB,mBAAO,OAAO,QAAQ,MAAM,MAAM;AAAA,UACpC,CAAC;AAEL,cAAI,WAAW,SAAS,GAAG;AACzB,kBAAM,IAAI,MAAM,iCACd,WAAW,KAAK,GAAG,IAAI,qFACuB;AAAA,UAClD;AAAA,QACF;AAEA,YAAI,QAAQ,WAAW;AACrB,6BAAmB,QAAQ;AAAA,QAC7B;AAGA,YAAI,QAAQ,iBAAiB,QAAW;AACtC,gCAAsB,QAAQ;AAAA,QAChC;AAEA,YAAI,QAAQ,QAAQ,QAAW;AAC7B,uBAAa,OAAO,QAAQ,GAAG;AAC/B,cAAI,aAAa,GAAG;AAClB,kBAAM,IAAI,MAAM,iDAAiD;AAAA,UACnE;AAAA,QACF;AAEA,YAAI,QAAQ,iBAAiB;AAC3B,cAAK,QAAQ,oBAAoB,iBAAiB,0BAA0B,eACvE,QAAQ,oBAAoB,iBAAiB,0BAA0B,SAAU;AACpF,8BAAkB,QAAQ;AAAA,UAC5B,OAAO;AACL,kBAAM,IAAI,MAAM,yCAAyC;AAAA,UAC3D;AAAA,QACF;AAEA,YAAI,QAAQ,SAAS;AACnB,cAAK,QAAQ,YAAY,iBAAiB,iBAAiB,YACtD,QAAQ,YAAY,iBAAiB,iBAAiB,OACtD,QAAQ,YAAY,iBAAiB,iBAAiB,UACtD,QAAQ,YAAY,iBAAiB,iBAAiB,MAAO;AAChE,sBAAU,QAAQ;AAAA,UACpB,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAEA,YAAI,QAAQ,OAAO;AACjB,cAAI,CAAC,gBAAgB,SAAS,QAAQ,KAAK,GAAG;AAC5C,kBAAM,IAAI,MAAM,+EAA+E;AAAA,UACjG;AACA,cAAI,QAAQ,MAAM,SAAS,IAAI;AAC7B,kBAAM,IAAI,MAAM,kFAAkF;AAAA,UACpG;AACA,kBAAQ,QAAQ;AAAA,QAClB;AAEA,YAAI,QAAQ,OAAO;AACjB,cAAI,OAAO,QAAQ,UAAU,YACxB,OAAO,QAAQ,MAAM,SAAS,UAAU;AAC3C,oBAAQ,QAAQ;AAAA,UAClB,OAAO;AACL,oBAAQ,KAAK,8FAA8F;AAAA,UAC7G;AAAA,QACF;AAEA,YAAI,QAAQ,OAAO;AACjB,cAAI,QAAQ,iBAAiB,MAAM,OAAO;AACxC,gBAAI,OAAO;AACT,sBAAQ,KAAK,+DAA+D;AAAA,YAC9E;AAEA,oBAAQ,QAAQ;AAAA,UAClB,OAAO;AACL,oBAAQ,KAAK,2EAA2E;AAAA,UAC1F;AAAA,QACF;AAEA,YAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,OAAO,eAAe,aAAa;AACrC,qBAAa;AAAA,MACf;AAEA,YAAM,iBAAiB;AAAA,QACrB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO,KAAK,YAAY,EAAE,QAAQ,SAAU,QAAQ;AAClD,uBAAe,QAAQ,MAAM,IAAI,aAAa,MAAM;AAAA,MACtD,CAAC;AACD,UAAI,iBAAiB;AAErB,UAAI,SAAS;AACX,cAAM,YAAY,iBACf,QAAQ,aAAa,KAAK,QAAQ,aAAa,KAAK,MAAM,SAAS,eAAe;AAErF,uBAAe,QAAQ,gBAAgB,IAAI,UAAU,WAAW;AAChE,uBAAe,QAAQ,cAAc,IAAI;AAEzC,YAAI,oBAAoB,iBAAiB,0BAA0B,aAAa;AAC9E,yBAAe,QAAQ,kBAAkB,IAAI,iBAAiB,0BAA0B;AAAA,QAC1F,WAAW,oBAAoB,iBAAiB,0BAA0B,SAAS;AACjF,yBAAe,QAAQ,kBAAkB,IAAI,iBAAiB,0BAA0B;AACxF,yBAAe,QAAQ,aAAa,UAAU,UAAU;AACxD,yBAAe,QAAQ,YAAY,IAAI,QAAQ,UAAU,eAAe,SAAS,WAAW;AAAA,QAC9F;AAEA,yBAAiB,UAAU;AAAA,MAC7B,OAAO;AACL,uBAAe,QAAQ,gBAAgB,IAAI;AAAA,MAC7C;AAEA,YAAM,QAAQ,aAAa,SAAS,WAAW,yCAAyC;AACxF,YAAM,QAAQ,aAAa,SAAS,WAAW,qCAAqC;AAEpF,UAAI,OAAO;AACT,YAAI,CAAC,kBAAkB;AACrB,kBAAQ,KAAK,0IAEkB;AAAA,QACjC,OAAO;AACL,yBAAe,QAAQ,gBAAgB,SAAS;AAAA,QAClD;AAAA,MACF,WAAW,qBAAqB;AAC9B,cAAM,YAAY,IAAI,MAAM,aAAa,QAAQ;AACjD,cAAM,WAAW,UAAU,WAAW,OACpC,UAAU;AAEZ,cAAM,eAAe,YAAY;AAAA,UAC/B;AAAA,UACA,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,UACpB,oBAAoB;AAAA,UACpB;AAAA,QACF;AAEA,uBAAe,QAAQ,gBAAgB,aAAa;AAEpD,YAAI,oBAAoB,iBAAiB,0BAA0B,SAAS;AAC1E,cAAI,eAAe,QAAQ,YAAY,GAAG;AACxC,2BAAe,QAAQ,YAAY,KAAK,MACtC,aAAa,YAAY;AAAA,UAC7B,OAAO;AACL,2BAAe,QAAQ,YAAY,IAAI,aAAa,YAAY;AAAA,UAClE;AAAA,QACF;AAAA,MACF,WAAW,SAAS,kBAAkB;AACpC,uBAAe,QAAQ,gBAAgB,SAAS;AAAA,MAClD;AAEA,qBAAe,QAAQ,UAAU;AAEjC,UAAI,OAAO;AACT,uBAAe,QAAQ,QAAQ;AAAA,MACjC;AAEA,qBAAe,OAAO;AACtB,qBAAe,WAAW,aAAa;AAEvC,UAAI,OAAO;AACT,uBAAe,QAAQ;AAAA,MACzB;AAEA,UAAI,OAAO;AACT,uBAAe,QAAQ;AAAA,MACzB;AAEA,UAAI,SAAS;AACX,uBAAe,UAAU;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAgBF,eAAW,UAAU,mBAAmB,SAAS,cAAc,SAAS,SAAS;AAC7E,UAAI;AACJ,UAAI;AACF,yBAAiB,KAAK,uBAAuB,cAAc,SAAS,OAAO;AAAA,MAC7E,SAAS,KAAK;AACZ,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC3B;AAEA,aAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,cAAM,eAAe,CAAC;AACtB,cAAM,WAAW,IAAI,MAAM,eAAe,QAAQ;AAClD,qBAAa,WAAW,SAAS;AACjC,qBAAa,OAAO,SAAS;AAC7B,qBAAa,OAAO,SAAS;AAE7B,qBAAa,UAAU,eAAe;AACtC,qBAAa,SAAS,eAAe;AAErC,YAAI,eAAe,SAAS;AAC1B,uBAAa,UAAU,eAAe;AAAA,QACxC;AAEA,YAAI,eAAe,OAAO;AACxB,uBAAa,QAAQ,eAAe;AAAA,QACtC;AAEA,YAAI,eAAe,OAAO;AACxB,gBAAM,EAAE,gBAAgB,IAAI;AAC5B,uBAAa,QAAQ,IAAI,gBAAgB,eAAe,KAAK;AAAA,QAC/D;AAEA,cAAM,cAAc,MAAM,QAAQ,cAAc,SAAS,cAAc;AACrE,cAAI,eAAe;AAEnB,uBAAa,GAAG,QAAQ,SAAS,OAAO;AACtC,4BAAgB;AAAA,UAClB,CAAC;AAED,uBAAa,GAAG,OAAO,WAAW;AAChC,gBAAI,aAAa,aAAa,OAAO,aAAa,aAAa,KAAK;AAClE,qBAAO,IAAI;AAAA,gBACT;AAAA,gBACA,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb;AAAA,gBACA,eAAe;AAAA,cACjB,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ;AAAA,gBACN,YAAY,aAAa;AAAA,gBACzB,MAAM;AAAA,gBACN,SAAS,aAAa;AAAA,cACxB,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAED,YAAI,eAAe,SAAS;AAC1B,sBAAY,GAAG,WAAW,WAAW;AACnC,wBAAY,QAAQ,IAAI,MAAM,gBAAgB,CAAC;AAAA,UACjD,CAAC;AAAA,QACH;AAEA,oBAAY,GAAG,SAAS,SAAS,GAAG;AAClC,iBAAO,CAAC;AAAA,QACV,CAAC;AAED,YAAI,eAAe,MAAM;AACvB,sBAAY,MAAM,eAAe,IAAI;AAAA,QACvC;AAEA,oBAAY,IAAI;AAAA,MAClB,CAAC;AAAA,IACH;AAEF,WAAO,UAAU;AAAA;AAAA;;;AC5ZjB,IAAAC,eAAA;AAAA;AAEA,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,aAAa;AACnB,QAAM,eAAe;AACrB,QAAM,mBAAmB;AAEzB,QAAM,UAAU,IAAI,WAAW;AAE/B,WAAO,UAAU;AAAA,MACf;AAAA,MACA,2BAA2B,iBAAiB;AAAA,MAC5C,SAAS,iBAAiB;AAAA,MAC1B,iBAAiB,YAAY;AAAA,MAC7B,mBAAmB,YAAY;AAAA,MAC/B,cAAc,QAAQ;AAAA,MACtB,iBAAiB,QAAQ;AAAA,MACzB,wBAAwB,QAAQ;AAAA,MAChC,kBAAkB,QAAQ,iBAAiB,KAAK,OAAO;AAAA,IACzD;AAAA;AAAA;", "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "timingSafeEqual", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "info", "req", "require_src"]}
<script lang="ts">
  import Button from '$lib/components/ui/button/button.svelte';
  import Separator from '$lib/components/ui/separator/separator.svelte';
  import { Check, X, Loader2, Info } from 'lucide-svelte';

  export let title: string;
  export let price: string;
  export let description: string;
  export let isPopular = false;
  export let ctaText: string;
  export let billingCycle: 'monthly' | 'annual';
  export let limits: Record<string, any> = {};
  export let features: any[] = [];
  export let onCtaClick: () => void;
  export let disabled = false;
  export let activePlan = false;
  export let loading = false;
  export let savings = 0;
</script>

<style>
  .tooltip-wrapper {
    position: relative;
    display: inline-block;
  }

  .tooltip {
    visibility: hidden;
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    white-space: nowrap;
    z-index: 50;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .tooltip-wrapper:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }
</style>

<div
  class="border-border rounded-lg border shadow-sm {isPopular
    ? 'border-primary shadow-primary/20 relative'
    : ''}">
  {#if isPopular}
    <div class="absolute inset-x-0 top-0 -translate-y-1/2 transform">
      <div
        class="bg-primary text-primary-foreground inline-block rounded-full px-4 py-1 text-xs font-semibold uppercase tracking-wider">
        Most Popular
      </div>
    </div>
  {/if}

  <div class="p-7">
    <h3 class="text-foreground text-3xl font-semibold">{title}</h3>
    <p class="text-muted-foreground mt-4 h-12">{description}</p>
  </div>
  <Separator class="bg-border my-4" />

  <!-- Pricing and Billing Cycle -->
  <div class="align-center mt-4 flex min-h-20 flex-col justify-center text-center">
    <div>
      <span class="font-semi-bold text-5xl">
        {#if title.toLowerCase() === 'free'}
          Free
        {:else}
          <span class="mr-1 align-super text-sm">$</span>{price}
          <span class="text-lg">/ month</span>
        {/if}
      </span>
      {#if title.toLowerCase() === 'custom'}<span>/ seat</span>{/if}
    </div>
    {#if title.toLowerCase() !== 'free'}
      <span class="text-muted-foreground mt-2 text-sm">Billed {billingCycle}</span>
      {#if savings > 0 && billingCycle === 'annual'}
        <span class="mt-1 text-xs font-medium text-green-600">Save {savings}%</span>
      {/if}
    {/if}

    <!-- CTA Button moved to top section for better visibility -->
    <div class="mt-6 px-8">
      <Button
        class="w-full"
        variant={isPopular ? 'default' : 'outline'}
        disabled={disabled || loading}
        onclick={onCtaClick}>
        {#if loading}
          <Loader2 class="mr-2 h-4 w-4 animate-spin" />
          Processing...
        {:else if activePlan}
          Current Plan
        {:else}
          {ctaText}
        {/if}
      </Button>
    </div>
  </div>

  <Separator class="bg-border mt-4" />

  <div class="mt-6 px-8">
    <div class="min-h-[300px]">
      <!-- Plan Limits -->
      <div class="mb-4">
        <h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Plan Limits</h4>
        <div class="grid grid-cols-1 gap-2 sm:grid-cols-3">
          <!-- Resume Scans -->
          <div class="rounded-md border p-3 text-center">
            <p class="text-muted-foreground mb-1 text-xs">Resume Scans</p>
            <p class="flex items-center justify-center text-lg font-semibold">
              {#if title.toLowerCase() === 'free'}
                10<span class="ml-1 text-xs font-normal">/mo</span>
              {:else if limits?.resumesPerMonth !== undefined && limits.resumesPerMonth !== null && limits.resumesPerMonth !== 'U'}
                {limits.resumesPerMonth}
                <span class="ml-1 text-xs font-normal">/mo</span>
              {:else}
                <span class="text-lg">∞</span>
              {/if}
            </p>
          </div>

          <!-- Job Profiles -->
          <div class="rounded-md border p-3 text-center">
            <p class="text-muted-foreground mb-1 text-xs">Job Profiles</p>
            <p class="flex items-center justify-center text-lg font-semibold">
              {#if title.toLowerCase() === 'free'}
                1
              {:else if limits?.profiles !== undefined && limits.profiles !== null && limits.profiles !== 'U'}
                {limits.profiles}
              {:else}
                <span class="text-lg">∞</span>
              {/if}
            </p>
          </div>

          <!-- Team Seats -->
          <div class="rounded-md border p-3 text-center">
            <p class="text-muted-foreground mb-1 text-xs">
              {#if title.toLowerCase() === 'free'}
                AI Credits
              {:else}
                Team Seats
              {/if}
            </p>
            <p class="flex items-center justify-center text-lg font-semibold">
              {#if title.toLowerCase() === 'free'}
                5<span class="ml-1 text-xs font-normal">/mo</span>
              {:else if limits?.seats !== undefined && limits.seats !== null && limits.seats !== 'U'}
                {limits.seats}
              {:else}
                <span class="text-lg">∞</span>
              {/if}
            </p>
          </div>
        </div>
      </div>

      <!-- Features -->
      <div>
        <h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Key Features</h4>
        <table class="w-full table-auto border-collapse text-sm">
          <tbody>
            {#each features as feature}
              <tr class="border-border border-t">
                <td class="text-foreground py-3 pr-4">
                  {#if feature.featureId === 'resume_scanner'}
                    Resume Scanner
                  {:else if feature.featureId === 'resume_builder'}
                    Resume Builder
                  {:else if feature.featureId === 'resume_ai'}
                    Resume AI
                  {:else if feature.featureId === 'job_search_profiles'}
                    Job Search Profiles
                  {:else if feature.featureId === 'job_save'}
                    Save Jobs
                  {:else if feature.featureId === 'job_alerts'}
                    Job Alerts
                  {:else if feature.featureId === 'tracker' || feature.featureId === 'application_tracker'}
                    Application Tracker
                  {:else if feature.featureId === 'cover_letter_generator'}
                    Cover Letter Generator
                  {:else if feature.featureId === 'dashboard'}
                    Dashboard Access
                  {:else if feature.featureId === 'profile'}
                    User Profile
                  {:else if feature.featureId === 'documents'}
                    Document Storage
                  {:else}
                    {feature.featureId
                      .split('_')
                      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                      .join(' ')}
                  {/if}
                </td>
                <td class="py-3 pl-4 text-right">
                  {#if feature.accessLevel === 'included'}
                    <Check class="text-success ml-auto h-4 w-4" />
                  {:else if feature.accessLevel === 'limited'}
                    <div class="flex items-center">
                      <span class="mr-1 text-xs text-amber-500">Limited</span>
                      <div class="tooltip-wrapper">
                        <Info class="text-muted-foreground h-3 w-3 cursor-help" />
                        <div class="tooltip">
                          <p class="text-xs">This feature has usage limits based on your plan</p>
                        </div>
                      </div>
                    </div>
                  {:else}
                    <X class="text-destructive ml-auto h-4 w-4" />
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <Separator class="bg-border mt-4" />
</div>

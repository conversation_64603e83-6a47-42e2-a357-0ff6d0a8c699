import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getPlansFromDatabase, initializePlansInDatabase } from '$lib/server/plan-sync';

/**
 * Public API endpoint to get all available plans
 * This is used by the public pricing page and doesn't require authentication
 */
export const GET: RequestHandler = async () => {
  try {
    // Initialize plans in database if they don't exist
    await initializePlansInDatabase();

    // Get all plans from the database
    const plans = await getPlansFromDatabase();

    // Return plans without any authentication check since this is public
    return json(plans);
  } catch (error) {
    console.error('Error loading plans for public API:', error);
    
    // Return fallback plans if database fails
    const fallbackPlans = [
      {
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        section: 'pro',
        monthlyPrice: 0,
        annualPrice: 0,
        popular: false,
        features: [],
        limits: {}
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features for active job seekers',
        section: 'pro',
        monthlyPrice: 1999,
        annualPrice: 19990,
        popular: true,
        features: [],
        limits: {}
      }
    ];
    
    return json(fallbackPlans);
  }
};

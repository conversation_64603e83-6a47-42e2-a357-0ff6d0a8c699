import { prisma } from '$lib/server/prisma';
import { verifySessionToken } from '$lib/server/auth.js';
import { redirect } from '@sveltejs/kit';
import { getPlansFromDatabase, initializePlansInDatabase } from '$lib/server/plan-sync';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ cookies, locals, url }) => {
  console.log('🔄 Pricing page server load function called');

  const token = cookies.get('auth_token');
  if (token) {
    const user = await verifySessionToken(token);
    if (user) {
      locals.user = user;
    }
  }

  // If no user found
  if (!locals.user) {
    locals.user = null;
  }

  // Get query parameters
  const preselectedPlanId = url.searchParams.get('plan');
  const preselectedBillingCycle =
    url.searchParams.get('billing') === 'annual' ? 'annual' : 'monthly';
  const preselectedSection = url.searchParams.get('section') === 'teams' ? 'teams' : 'pro';

  console.log('🔄 Initializing plans in database...');

  // Initialize plans in database if they don't exist
  try {
    await initializePlansInDatabase();
    console.log('✅ Plans initialization completed');
  } catch (error) {
    console.error('❌ Error initializing plans:', error);
  }

  // Get plans from database
  let plans;
  try {
    console.log('🔄 Getting plans from database...');
    plans = await getPlansFromDatabase();

    // Debug plans from database
    console.log('📊 Plans from database:', plans);
    console.log('📊 Number of plans:', plans.length);
    console.log(
      '📊 Individual plans:',
      plans.filter((p) => p.section === 'pro').map((p) => p.id)
    );
    console.log(
      '📊 Team plans:',
      plans.filter((p) => p.section === 'teams').map((p) => p.id)
    );

    // Check if free plan exists
    const freePlanExists = plans.some((p) => p.id === 'free');
    console.log('📊 Free plan exists:', freePlanExists);

    // If free plan doesn't exist, add it
    if (!freePlanExists) {
      console.log('➕ Adding free plan manually');
      plans.push({
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        section: 'pro',
        monthlyPrice: 0,
        annualPrice: 0,
        popular: false,
        features: [],
        limits: {},
      });
    }

    // If no plans exist, create some default ones
    if (plans.length === 0) {
      console.log('➕ No plans found, creating default plans');
      plans = [
        {
          id: 'free',
          name: 'Free',
          description: 'Basic features for personal use',
          section: 'pro',
          monthlyPrice: 0,
          annualPrice: 0,
          popular: false,
          features: [],
          limits: {},
        },
        {
          id: 'pro',
          name: 'Pro',
          description: 'Advanced features for active job seekers',
          section: 'pro',
          monthlyPrice: 1999,
          annualPrice: 19990,
          popular: true,
          features: [],
          limits: {},
        },
      ];
    }
  } catch (error) {
    console.error('❌ Error loading plans:', error);
    // Create default plans as fallback
    console.log('➕ Creating fallback plans due to error');
    plans = [
      {
        id: 'free',
        name: 'Free',
        description: 'Basic features for personal use',
        section: 'pro',
        monthlyPrice: 0,
        annualPrice: 0,
        popular: false,
        features: [],
        limits: {},
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features for active job seekers',
        section: 'pro',
        monthlyPrice: 1999,
        annualPrice: 19990,
        popular: true,
        features: [],
        limits: {},
      },
    ];
  }

  console.log('✅ Final plans to return:', plans.length, 'plans');
  console.log(
    '✅ Plans:',
    plans.map((p) => ({ id: p.id, name: p.name, section: p.section }))
  );

  return {
    user: locals.user,
    plans,
    preselectedPlanId,
    preselectedBillingCycle,
    preselectedSection,
  };
};
